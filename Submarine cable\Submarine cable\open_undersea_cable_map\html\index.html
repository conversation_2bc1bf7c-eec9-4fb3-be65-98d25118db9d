
<!DOCTYPE html>
<html>
<head>
    <title>Submarine Cable Map</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            font-family: Arial, sans-serif;
        }

        #map {
            width: 75%;
            height: 100vh;
        }

        .sidebar {
            width: 25%;
            height: 100vh;
            background-color: #f4f4f4;
            border-left: 1px solid #ddd;
            overflow-y: auto;
            padding: 20px;
        }

        .sidebar h2 {
            font-size: 18px;
            margin-top: 0;
        }

        .sidebar ul {
            list-style: none;
            padding-left: 0;
        }

        .sidebar li {
            padding: 8px 0;
            border-bottom: 1px solid #ccc;
            font-size: 14px;
        }

        /* Search interface styling */
        .search-control {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 15px;
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
            min-width: 280px;
            max-width: 320px;
        }

        .search-control h4 {
            margin: 0 0 12px 0;
            color: #2c3e50;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
        }

        .search-input-group {
            margin-bottom: 10px;
        }

        .search-input-group label {
            display: block;
            margin-bottom: 4px;
            color: #495057;
            font-size: 12px;
            font-weight: 500;
        }

        .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            color: #495057;
            background-color: #fff;
            box-sizing: border-box;
        }

        .search-input:focus {
            outline: none;
            border-color: #2E86AB;
            box-shadow: 0 0 0 2px rgba(46, 134, 171, 0.2);
        }

        .search-button {
            width: 100%;
            padding: 10px;
            background-color: #2E86AB;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 8px;
            transition: background-color 0.2s;
        }

        .search-button:hover {
            background-color: #1e5f7a;
        }

        .search-button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        .search-results {
            margin-top: 12px;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-size: 12px;
            color: #495057;
            display: none;
        }

        .search-results.show {
            display: block;
        }

        .search-results.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .search-results.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .clear-search-button {
            width: 100%;
            padding: 6px;
            background-color: #6c757d;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            margin-top: 6px;
            transition: background-color 0.2s;
        }

        .clear-search-button:hover {
            background-color: #545b62;
        }

        /* Responsive design for mobile devices */
        @media (max-width: 768px) {
            .sidebar {
                display: none;
            }

            #map {
                width: 100%;
            }

            .search-control {
                min-width: 250px;
                max-width: 280px;
                padding: 12px;
            }
        }

        @media (max-width: 480px) {
            .search-control {
                min-width: 220px;
                max-width: 250px;
                padding: 10px;
            }

            .search-control h4 {
                font-size: 14px;
            }
        }
    </style>
</head>
<body>

    <!-- Map Section -->
    <div id="map"></div>

    <!-- Right Panel Sidebar -->
    <div class="sidebar">
        <h2>Submarine Cables</h2>

        <!-- Search Interface -->
        <div class="search-control" style="margin-bottom: 20px;">
            <h4>🔍 Cables Search</h4>
            <p style="font-size: 12px; color: #666; margin-bottom: 12px;">
                Search for submarine cables connecting African and European countries.
            </p>
            <div class="search-input-group">
                <label for="fromCountry">From Country:</label>
                <input type="text" id="fromCountry" class="search-input" placeholder="e.g., South Africa">
            </div>
            <div class="search-input-group">
                <label for="toCountry">To Country:</label>
                <input type="text" id="toCountry" class="search-input" placeholder="e.g., United Kingdom">
            </div>
            <button id="searchButton" class="search-button">Search Cables</button>
            <button id="clearSearchButton" class="clear-search-button" style="display: none;">Clear Search</button>
            <div id="searchResults" class="search-results"></div>
        </div>

        <div style="font-size: 12px; color: #666; margin-top: 20px;">
            <p><strong>Example searches:</strong></p>
            <ul style="padding-left: 15px;">
                <li>South Africa → United Kingdom</li>
                <li>Egypt → Italy</li>
                <li>Morocco → Spain</li>
                <li>Kenya → France</li>
            </ul>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>

    

    <script>
        // Initialize the map with world bounds to prevent infinite horizontal scrolling

        const map = L.map('map', {
            worldCopyJump: false, // Prevents horizontal world repetition
            maxBounds: [[-90, -180], [90, 180]], // Restricts panning to one world
            maxBoundsViscosity: 1.0, // Prevents dragging outside bounds
            minZoom: 2, // Prevents zooming out to see multiple worlds
            maxZoom: 19
        }).setView([20, 0], 2);
        
        // Add CartoDB Positron tile layer (clean, well-labeled)
        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map);

        // Create layer groups
        const cableLayer = L.layerGroup().addTo(map);
        const landingPointLayer = L.layerGroup().addTo(map);

        // Professional color palette inspired by submarinecablemap.com
        // Muted, professional colors that are easy on the eyes
        const professionalColorPalette = [
            '#2E86AB','#A23B72', '#F18F01', 
            '#C73E1D','#6C757D', '#495057', 
            '#4A90A4', '#8E44AD', '#D35400', 
            '#27AE60', '#2C3E50', '#8B4513',
            '#556B2F', '#4682B4', '#CD853F', 
            '#708090', '#2F4F4F', '#800080', 
            '#B22222', '#228B22', '#4169E1', 
            '#DC143C', '#FF8C00', '#9932CC', 
            '#8FBC8F', '#483D8B', '#2E8B57', 
            '#B8860B', '#A0522D', '#1E90FF', 
            '#32CD32', '#FF6347','#4B0082', 
            '#DAA520', '#008B8B', '#9400D3', 
            '#FF4500', '#2E8B57', '#8B008B', 
            '#556B2F' 
        ];

        // Function to generate consistent professional colors for cables
        function getProfessionalCableColor(cableId, index) {
            let hash = 0;
            for (let i = 0; i < cableId.length; i++) {
                const char = cableId.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32-bit integer
            }

            const colorIndex = (Math.abs(hash) + index) % professionalColorPalette.length;
            return professionalColorPalette[colorIndex];
        }

       
        // Americas countries (North, Central, and South America)
        const americasCountries = new Set([
            // North America
            'United States', 'Canada', 'Mexico', 'Greenland',
            // Central America and Caribbean
            'Guatemala', 'Belize', 'El Salvador', 'Honduras', 'Nicaragua', 'Costa Rica', 'Panama',
            'Cuba', 'Jamaica', 'Haiti', 'Dominican Republic', 'Puerto Rico', 'Bahamas', 'Barbados',
            'Trinidad and Tobago', 'Grenada', 'Saint Vincent and the Grenadines', 'Saint Lucia',
            'Dominica', 'Antigua and Barbuda', 'Saint Kitts and Nevis', 'Martinique', 'Guadeloupe',
            'Saint Barthélemy', 'Saint Martin', 'Sint Maarten', 'Anguilla', 'British Virgin Islands',
            'Virgin Islands (U.S.)', 'Virgin Islands (U.K.)', 'Cayman Islands', 'Turks and Caicos Islands',
            'Aruba', 'Curaçao', 'Bonaire, Sint Eustatius and Saba', 'Netherlands', 'French Guiana',
            // South America
            'Brazil', 'Argentina', 'Chile', 'Peru', 'Colombia', 'Venezuela', 'Ecuador', 'Bolivia',
            'Paraguay', 'Uruguay', 'Guyana', 'Suriname', 'French Guiana'
        ]);

        // Define Asia-Pacific countries and territories
        const asiaPacificCountries = new Set([
            // East Asia
            'China', 'Japan', 'South Korea', 'North Korea', 'Taiwan', 'Hong Kong', 'Macau', 'Mongolia',
            // Southeast Asia
            'Singapore', 'Indonesia', 'Philippines', 'Malaysia', 'Vietnam', 'Thailand', 'Myanmar',
            'Cambodia', 'Laos', 'Brunei', 'Timor-Leste',
            // South Asia
            'India', 'Pakistan', 'Bangladesh', 'Sri Lanka', 'Nepal', 'Bhutan', 'Maldives', 'Afghanistan',
            // Oceania and Pacific Islands
            'Australia', 'New Zealand', 'Papua New Guinea', 'Fiji', 'Solomon Islands', 'Vanuatu',
            'New Caledonia', 'Samoa', 'Tonga', 'Kiribati', 'Tuvalu', 'Nauru', 'Palau', 'Marshall Islands',
            'Micronesia', 'Cook Islands', 'French Polynesia', 'Wallis and Futuna', 'American Samoa',
            'Guam', 'Northern Mariana Islands', 'Cocos (Keeling) Islands', 'Christmas Island'
        ]);

        // Function to check if a cable has landing points in the Americas
        function isAmericasCable(cableId) {
            return false; 
        }

        // Function to check if coordinates are in Americas region (rough approximation)
        function isInAmericasRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            // Recursive function to check coordinates at any nesting level
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    // Americas longitude range (including Alaska and eastern Brazil)
                    // Expanded range to be more inclusive: -180° to -25°
                    return lng >= -180 && lng <= -25;
                }

                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }

                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Function to check if coordinates are in Asia-Pacific region
        function isInAsiaPacificRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            // Recursive function to check coordinates at any nesting level
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];

                    // Asia-Pacific region boundaries (adjusted to exclude East Africa and Middle East):
                    // Main Asia-Pacific: Longitude 65°E to 180°E, Latitude -50°S to 80°N
                    // Pacific extension: Longitude -180°E to -120°W (for Pacific islands), Latitude -50°S to 80°N
                    const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
                    const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

                    return inMainAsiaPacific || inPacificExtension;
                }

                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }

                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Fetch and display cable routes
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                console.log(`Total cables before filtering: ${data.features.length}`);

                // Store the original data for search functionality
                allCableData = data;
                console.log('Cable data loaded for search:', data.features.length, 'cables');

                // Critical African cables that should always be preserved
                const criticalAfricanCables = new Set([
                    '2africa',
                    'west-africa-cable-system-wacs',
                    'africa-coast-to-europe-ace',
                    'eastern-africa-submarine-system-eassy',
                    'asia-africa-europe-1-aae-1',
                    'safe',
                    'sat-3wasc',
                    'equiano',
                    'africa-1',
                    'seychelles-to-east-africa-system-seas',
                    'the-east-african-marine-system-teams',
                    'seacomtata-tgn-eurasia'
                ]);

                // Filter out cables in Americas and Asia-Pacific regions, but preserve critical African infrastructure
                const filteredFeatures = data.features.filter(feature => {
                    // Always preserve critical African cables
                    if (criticalAfricanCables.has(feature.properties.id)) {
                        console.log(`Preserving critical African cable: ${feature.properties.name}`);
                        return true;
                    }

                    // Check if cable coordinates are in Americas or Asia-Pacific regions
                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isInAmericasRegion(feature.geometry.coordinates);
                        const isAsiaPacific = isInAsiaPacificRegion(feature.geometry.coordinates);

                        if (isAmericas) {
                            console.log(`Filtering out Americas cable: ${feature.properties.name}`);
                            return false;
                        }
                        if (isAsiaPacific) {
                            console.log(`Filtering out Asia-Pacific cable: ${feature.properties.name}`);
                            return false;
                        }
                        return true; // Keep cable if it's not in filtered regions
                    }
                    return true; // Keep cable if we can't determine location
                });

                console.log(`Total cables after filtering: ${filteredFeatures.length}`);

                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };

                // Create cables with professional color scheme and interactive labeling
                L.geoJSON(filteredData, {
                    style: function(feature) {
                        // Get professional color for this cable
                        const cableIndex = filteredData.features.indexOf(feature);
                        const cableColor = getProfessionalCableColor(feature.properties.id, cableIndex);

                        return {
                            color: cableColor,
                            weight: 2.5,
                            opacity: 0.85,
                            // Use subtle dashed line for planned cables
                            dashArray: feature.properties.is_planned ? '8, 4' : null
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        // Store original style for hover effects
                        const cableIndex = filteredData.features.indexOf(feature);
                        const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);

                        layer.on({
                            mouseover: function(e) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1,
                                    color: originalColor
                                });
                                info.update(feature.properties);
                            },
                            mouseout: function(e) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 2.5,
                                    opacity: 0.85,
                                    color: originalColor
                                });
                                info.update();
                            },
                            click: function(e) {
                                // Show detailed popup instead of simple label
                                layer.openPopup();

                                // Prevent event bubbling to map click
                                L.DomEvent.stopPropagation(e);
                            }
                        });

                        // Create enhanced popup with professional styling
                        let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                        popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                        popupContent += '<div style="display: flex; align-items: center; margin-bottom: 6px;">';
                        popupContent += '<span style="color: ' + originalColor + '; font-size: 16px; margin-right: 6px;">●</span>';
                        popupContent += '<span style="font-size: 12px; color: #6c757d;">Cable Color</span>';
                        popupContent += '</div>';

                        if (feature.properties.rfs) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                        }
                        if (feature.properties.length) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                        }
                        if (feature.properties.owners) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                        }

                        popupContent += '</div>';

                        layer.bindPopup(popupContent);
                    }
                }).addTo(cableLayer);
            });

        // Function to check if landing point coordinates are in Americas region
        function isLandingPointInAmericas(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            // Americas longitude range (including Alaska and eastern Brazil)
            return lng >= -180 && lng <= -25;
        }

        // Function to check if landing point coordinates are in Asia-Pacific region
        function isLandingPointInAsiaPacific(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            const lat = coordinates[1];

            // Main Asia-Pacific: Longitude 65°E to 180°E, Latitude -50°S to 80°N
            // Pacific extension: Longitude -180°E to -120°W (for Pacific islands), Latitude -50°S to 80°N
            const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
            const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

            return inMainAsiaPacific || inPacificExtension;
        }

        // Fetch and display landing points
        fetch('../landing-point/landing-point-geo.json')
            .then(response => response.json())
            .then(data => {
                console.log(`Total landing points before filtering: ${data.features.length}`);

                // Store the original data for search functionality
                allLandingPointData = data;
                console.log('Landing point data loaded for search:', data.features.length, 'landing points');

                // Critical African landing points that should always be preserved
                const criticalAfricanLandingPoints = new Set([
                    'cape-town-south-africa',
                    'mtunzini-south-africa',
                    'port-elizabeth-south-africa',
                    'gqeberha-south-africa',
                    'mombasa-kenya',
                    'dar-es-salaam-tanzania',
                    'djibouti-city-djibouti',
                    'lagos-nigeria',
                    'accra-ghana',
                    'dakar-senegal',
                    'casablanca-morocco',
                    'alexandria-egypt',
                    'port-said-egypt',
                    'zafarana-egypt',
                    'mumbai-india',
                    'maputo-mozambique',
                    'jeddah-saudi-arabia'
                ]);

                // Filter out landing points in Americas and Asia-Pacific regions, but preserve critical African infrastructure
                const filteredFeatures = data.features.filter(feature => {
                    // Always preserve critical African landing points
                    if (criticalAfricanLandingPoints.has(feature.properties.id)) {
                        console.log(`Preserving critical African landing point: ${feature.properties.name}`);
                        return true;
                    }

                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isLandingPointInAmericas(feature.geometry.coordinates);
                        const isAsiaPacific = isLandingPointInAsiaPacific(feature.geometry.coordinates);

                        if (isAmericas) {
                            console.log(`Filtering out Americas landing point: ${feature.properties.name}`);
                            return false;
                        }
                        if (isAsiaPacific) {
                            console.log(`Filtering out Asia-Pacific landing point: ${feature.properties.name}`);
                            return false;
                        }
                        return true; 
                    }
                    return true; 
                });

                console.log(`Total landing points after filtering: ${filteredFeatures.length}`);

                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };

                L.geoJSON(filteredData, {
                    pointToLayer: (feature, latlng) => {
                        return L.circleMarker(latlng, {
                            radius: 5,
                            fillColor: '#FF0000',
                            color: '#000',
                            weight: 1,
                            opacity: 1,
                            fillOpacity: 0.8
                        });
                    },
                    onEachFeature: (feature, layer) => {
                        if (feature.properties.name) {
                            layer.bindPopup(`<b>${feature.properties.name}</b><br>
                                ${feature.properties.country || ''}`);
                        }
                    }
                }).addTo(landingPointLayer);
            });

        // Add layer control with improved tile options
        const baseMaps = {
            "CartoDB Positron": L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                attribution: '© OpenStreetMap contributors © CARTO',
                subdomains: 'abcd',
                maxZoom: 19
            }),
            "Esri World Street": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri — Source: Esri, DeLorme, NAVTEQ, USGS, Intermap, iPC, NRCAN, Esri Japan, METI, Esri China (Hong Kong), Esri (Thailand), TomTom, 2012',
                maxZoom: 19
            }),
            "CartoDB Voyager": L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
                attribution: '© OpenStreetMap contributors © CARTO',
                subdomains: 'abcd',
                maxZoom: 19
            }),
            "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 19
            }),
            "Satellite": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri — Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
                maxZoom: 19
            })
        };

        const overlayMaps = {
            "Submarine Cables": cableLayer,
            "Landing Points": landingPointLayer
        };

        L.control.layers(baseMaps, overlayMaps).addTo(map);

        // Add scale control
        L.control.scale().addTo(map);

        // Search functionality variables
        let allCableData = null;
        let allLandingPointData = null;
        let highlightedCables = [];

        // African and European countries for search validation
        const africanCountries = new Set([
            'algeria', 'angola', 'benin', 'botswana', 'burkina faso', 'burundi', 'cameroon', 'cape verde',
            'central african republic', 'chad', 'comoros', 'congo', 'democratic republic of congo', 'djibouti',
            'egypt', 'equatorial guinea', 'eritrea', 'ethiopia', 'gabon', 'gambia', 'ghana', 'guinea',
            'guinea-bissau', 'ivory coast', 'kenya', 'lesotho', 'liberia', 'libya', 'madagascar', 'malawi',
            'mali', 'mauritania', 'mauritius', 'morocco', 'mozambique', 'namibia', 'niger', 'nigeria',
            'rwanda', 'sao tome and principe', 'senegal', 'seychelles', 'sierra leone', 'somalia',
            'south africa', 'south sudan', 'sudan', 'swaziland', 'tanzania', 'togo', 'tunisia', 'uganda',
            'zambia', 'zimbabwe'
        ]);

        const europeanCountries = new Set([
            'albania', 'andorra', 'austria', 'belarus', 'belgium', 'bosnia and herzegovina', 'bulgaria',
            'croatia', 'cyprus', 'czech republic', 'denmark', 'estonia', 'finland', 'france', 'germany',
            'greece', 'hungary', 'iceland', 'ireland', 'italy', 'kosovo', 'latvia', 'liechtenstein',
            'lithuania', 'luxembourg', 'malta', 'moldova', 'monaco', 'montenegro', 'netherlands',
            'north macedonia', 'norway', 'poland', 'portugal', 'romania', 'san marino', 'serbia',
            'slovakia', 'slovenia', 'spain', 'sweden', 'switzerland', 'ukraine', 'united kingdom', 'vatican city'
        ]);

        // Function to normalize country names for comparison
        function normalizeCountryName(countryName) {
            return countryName.toLowerCase().trim()
                .replace(/\s+/g, ' ')
                .replace(/[^\w\s]/g, '');
        }

        // Function to validate if countries are African or European
        function validateCountries(fromCountry, toCountry) {
            const fromNormalized = normalizeCountryName(fromCountry);
            const toNormalized = normalizeCountryName(toCountry);

            const fromValid = africanCountries.has(fromNormalized) || europeanCountries.has(fromNormalized);
            const toValid = africanCountries.has(toNormalized) || europeanCountries.has(toNormalized);

            return { fromValid, toValid, fromNormalized, toNormalized };
        }

        // Function to check if a cable connects two countries
        function cableConnectsCountries(cable, fromCountry, toCountry, landingPoints) {
            if (!cable.properties) return false;

            // Try multiple ways to find landing points for this cable
            const cableId = cable.properties.id;
            const cableName = cable.properties.name;

            // Find landing points for this cable using different matching strategies
            let cableLandingPoints = [];

            // Strategy 1: Match by cable_id
            cableLandingPoints = landingPoints.filter(lp =>
                lp.properties && (
                    lp.properties.cable_id === cableId ||
                    lp.properties.cable_name === cableName ||
                    (lp.properties.cables && lp.properties.cables.includes(cableId))
                )
            );

            // Strategy 2: If no matches, try partial name matching
            if (cableLandingPoints.length === 0 && cableName) {
                cableLandingPoints = landingPoints.filter(lp =>
                    lp.properties && lp.properties.cable_name &&
                    (lp.properties.cable_name.toLowerCase().includes(cableName.toLowerCase()) ||
                     cableName.toLowerCase().includes(lp.properties.cable_name.toLowerCase()))
                );
            }

            if (cableLandingPoints.length < 2) return false;

            // Get countries from landing points
            const countries = cableLandingPoints.map(lp =>
                normalizeCountryName(lp.properties.country || lp.properties.name || '')
            ).filter(country => country.length > 0);

            const uniqueCountries = [...new Set(countries)];

            // Check if both target countries are in the cable's landing points
            return uniqueCountries.includes(fromCountry) && uniqueCountries.includes(toCountry);
        }

        // Function to highlight search results
        function highlightSearchResults(matchingCables) {
            console.log('Highlighting cables:', matchingCables.length);

            // Clear previous highlights
            clearSearchHighlights();

            if (matchingCables.length === 0) return;

            // Create highlighted layers
            matchingCables.forEach((cable, index) => {
                const cableColor = getProfessionalCableColor(cable.properties.id, index);

                const highlightedCable = L.geoJSON(cable, {
                    style: {
                        color: cableColor,
                        weight: 5,
                        opacity: 1,
                        dashArray: '10, 5'
                    },
                    onEachFeature: function(feature, layer) {
                        // Add popup with cable info
                        let popupContent = `<div style="font-family: sans-serif;">
                            <h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">${feature.properties.name || feature.properties.id}</h4>
                            <div style="font-weight: bold; color: #2E86AB;">Search Result</div>`;

                        if (feature.properties.rfs) {
                            popupContent += `<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ${feature.properties.rfs}</div>`;
                        }
                        if (feature.properties.length) {
                            popupContent += `<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ${feature.properties.length} km</div>`;
                        }
                        if (feature.properties.owners) {
                            popupContent += `<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ${feature.properties.owners}</div>`;
                        }

                        popupContent += '</div>';
                        layer.bindPopup(popupContent);
                    }
                }).addTo(map);

                // Store reference for clearing
                highlightedCables.push(highlightedCable);
            });

            // Zoom to fit the highlighted cables if possible
            if (highlightedCables.length > 0) {
                try {
                    const group = L.featureGroup(highlightedCables);
                    const bounds = group.getBounds();
                    if (bounds.isValid()) {
                        map.fitBounds(bounds, { padding: [50, 50] });
                    }
                } catch (e) {
                    console.error('Error zooming to bounds:', e);
                }
            }
        }

        // Function to clear search highlights
        function clearSearchHighlights() {
            highlightedCables.forEach(layer => {
                if (map.hasLayer(layer)) {
                    map.removeLayer(layer);
                }
            });
            highlightedCables = [];
        }

        // Search button event listener
        document.getElementById('searchButton').addEventListener('click', function() {
            console.log('Search button clicked');

            const fromCountry = document.getElementById('fromCountry').value.trim();
            const toCountry = document.getElementById('toCountry').value.trim();
            const resultsDiv = document.getElementById('searchResults');
            const clearButton = document.getElementById('clearSearchButton');

            console.log('Search inputs:', fromCountry, toCountry);

            // Reset results
            resultsDiv.className = 'search-results';
            resultsDiv.style.display = 'none';

            if (!fromCountry || !toCountry) {
                resultsDiv.innerHTML = 'Please enter both countries.';
                resultsDiv.className = 'search-results error show';
                return;
            }

            // Validate countries
            const validation = validateCountries(fromCountry, toCountry);
            console.log('Validation result:', validation);

            if (!validation.fromValid || !validation.toValid) {
                let errorMsg = 'Please enter valid African or European countries. ';
                if (!validation.fromValid) errorMsg += `"${fromCountry}" is not recognized. `;
                if (!validation.toValid) errorMsg += `"${toCountry}" is not recognized.`;

                resultsDiv.innerHTML = errorMsg;
                resultsDiv.className = 'search-results error show';
                return;
            }

            if (!allCableData || !allLandingPointData) {
                console.log('Data not loaded yet:', !!allCableData, !!allLandingPointData);
                resultsDiv.innerHTML = 'Cable data is still loading. Please try again in a moment.';
                resultsDiv.className = 'search-results error show';
                return;
            }

            console.log('Searching cables...', allCableData.features.length, 'cables available');
            console.log('Landing points available:', allLandingPointData.features.length);

            // Search for matching cables
            const matchingCables = allCableData.features.filter(cable =>
                cableConnectsCountries(cable, validation.fromNormalized, validation.toNormalized, allLandingPointData.features)
            );

            console.log('Matching cables found:', matchingCables.length);

            if (matchingCables.length > 0) {
                highlightSearchResults(matchingCables);

                const cableNames = matchingCables.map(cable => cable.properties.name || cable.properties.id);
                resultsDiv.innerHTML = `Found ${matchingCables.length} cable(s) connecting ${fromCountry} and ${toCountry}:<br><br>` +
                    cableNames.map(name => `• ${name}`).join('<br>');
                resultsDiv.className = 'search-results success show';
                clearButton.style.display = 'block';
            } else {
                resultsDiv.innerHTML = `No direct cable connections found between ${fromCountry} and ${toCountry}.`;
                resultsDiv.className = 'search-results error show';
            }
        });

        // Clear search button event listener
        document.getElementById('clearSearchButton').addEventListener('click', function() {
            clearSearchHighlights();
            document.getElementById('fromCountry').value = '';
            document.getElementById('toCountry').value = '';
            document.getElementById('searchResults').style.display = 'none';
            document.getElementById('clearSearchButton').style.display = 'none';
        });


    </script>
</body>
</html>
