{"id": "france", "name": "France", "cables": [{"id": "2africa", "name": "2Africa", "rfs_year": 2023, "is_planned": true}, {"id": "africa-1", "name": "Africa-1", "rfs_year": 2023, "is_planned": true}, {"id": "africa-coast-to-europe-ace", "name": "Africa Coast to Europe (ACE)", "rfs_year": 2012, "is_planned": false}, {"id": "amitie", "name": "Amitie", "rfs_year": 2023, "is_planned": true}, {"id": "apollo", "name": "Apollo", "rfs_year": 2003, "is_planned": false}, {"id": "asia-africa-europe-1-aae-1", "name": "Asia Africa Europe-1 (AAE-1)", "rfs_year": 2017, "is_planned": false}, {"id": "atlas-offshore", "name": "Atlas Offshore", "rfs_year": 2007, "is_planned": false}, {"id": "blue", "name": "Blue", "rfs_year": 2024, "is_planned": true}, {"id": "circe-south", "name": "Circe South", "rfs_year": 1999, "is_planned": false}, {"id": "corse-continent-4-cc4", "name": "Corse-Continent 4 (CC4)", "rfs_year": 1992, "is_planned": false}, {"id": "corse-continent-5-cc5", "name": "Corse-Continent 5 (CC5)", "rfs_year": 1995, "is_planned": false}, {"id": "crosschannel-fibre", "name": "CrossChannel Fibre", "rfs_year": 2021, "is_planned": false}, {"id": "dunant", "name": "<PERSON><PERSON>", "rfs_year": 2021, "is_planned": false}, {"id": "flag-atlantic-1-fa-1", "name": "FLAG Atlantic-1 (FA-1)", "rfs_year": 2001, "is_planned": false}, {"id": "groix-4", "name": "Groix 4", "rfs_year": 2022, "is_planned": false}, {"id": "hawk", "name": "Hawk", "rfs_year": 2011, "is_planned": false}, {"id": "high-capacity-undersea-guernsey-optical-fibre-hugo", "name": "High-capacity Undersea Guernsey Optical-fibre (HUGO)", "rfs_year": 2007, "is_planned": false}, {"id": "imewe", "name": "IMEWE", "rfs_year": 2010, "is_planned": false}, {"id": "ingrid", "name": "INGRID", "rfs_year": 2004, "is_planned": false}, {"id": "med-cable-network", "name": "Med Cable Network", "rfs_year": 2005, "is_planned": false}, {"id": "medusa-submarine-cable-system", "name": "Medusa Submarine Cable System", "rfs_year": 2024, "is_planned": true}, {"id": "peace-cable", "name": "PEACE Cable", "rfs_year": 2022, "is_planned": false}, {"id": "seamewe-3", "name": "SeaMeWe-3", "rfs_year": 1999, "is_planned": false}, {"id": "seamewe-4", "name": "SeaMeWe-4", "rfs_year": 2005, "is_planned": false}, {"id": "seamewe-5", "name": "SeaMeWe-5", "rfs_year": 2016, "is_planned": false}, {"id": "seamewe-6", "name": "SeaMeWe-6", "rfs_year": 2025, "is_planned": true}, {"id": "te-northtgn-eurasiaseacomalexandrosmedex", "name": "TE North/TGN-Eurasia/SEACOM/Alexandros/Medex", "rfs_year": 2011, "is_planned": false}], "landing_points": ["northport-ny-united-states", "island-park-ny-united-states", "deep-water-bay-china", "taipa-china", "batangas-philippines", "danang-vietnam", "tungku-brunei", "mersing-malaysia", "tuas-singapore", "okinawa-japan", "shanghai-china", "toucheng-taiwan", "fangshan-taiwan", "ancol-indonesia", "perth-wa-australia", "penang-malaysia", "satun-thailand", "pyapon-myanmar", "mt-lavinia-sri-lanka", "cochin-india", "mumbai-india", "karachi-pakistan", "muscat-oman", "fujairah-united-arab-emirates", "djibouti-city-djibouti", "suez-egypt", "alexandria-egypt", "yeroskipos-cyprus", "marmaris-turkey", "mazara-del-vallo-italy", "sesimbra-portugal", "penmarch-france", "goonhilly-downs-united-kingdom", "medan-indonesia", "jeddah-saudi-arabia", "skewjack-united-kingdom", "chania-greece", "ttouan-morocco", "ostend-belgium", "plerin-france", "pevensey-bay-united-kingdom", "cayeux-sur-mer-france", "chennai-india", "colombo-sri-lanka", "palermo-italy", "bizerte-tunisia", "annaba-algeria", "marseille-france", "coxs-bazar-bangladesh", "melaka-malaysia", "algiers-algeria", "cannes-france", "lle-rousse-france", "la-seyne-france", "ajaccio-france", "asilah-morocco", "oran-algeria", "catania-italy", "tripoli-lebanon", "nouakchott-mauritania", "dakar-senegal", "banjul-gambia", "conakry-guinea", "freetown-sierra-leone", "monrovia-liberia", "abidjan-cte-divoire", "accra-ghana", "cotonou-benin", "lagos-nigeria", "libreville-gabon", "bata-equatorial-guinea", "pentaskhinos-cyprus", "abu-talat-egypt", "kribi-cameroon", "lannion-france", "porthcurno-united-kingdom", "pembroke-guernsey", "saints-bay-guernsey", "greve-de-lecq-jersey", "havelet-bay-guernsey", "archirondel-jersey", "surville-france", "zafarana-egypt", "doha-qatar", "songkhla-thailand", "sihanoukville-cambodia", "aden-yemen", "al-bustan-oman", "toulon-france", "yanbu-saudi-arabia", "haramous-djibouti", "qalhat-oman", "ngwe-saung-myanmar", "kuakata-bangladesh", "al-hudaydah-yemen", "matara-sri-lanka", "bari-italy", "dumai-indonesia", "cape-daguilar-china", "vung-tau-vietnam", "shirley-ny-united-states", "manasquan-nj-united-states", "duynefontein-south-africa", "carcavelos-portugal", "mombasa-kenya", "gwadar-pakistan", "suro-guinea-bissau", "berbera-somalia", "kalba-united-arab-emirates", "virginia-beach-va-united-states", "victoria-seychelles", "bosaso-somalia", "mogadishu-somalia", "kismayo-somalia", "hobyo-somalia", "saint-hilair<PERSON>-<PERSON>-riez-france", "bude-united-kingdom", "granadilla-canary-islands-spain", "geoje-south-korea", "brighton-united-kingdom", "ras-ghareb-egypt", "port-said-egypt", "genoa-italy", "barcelona-spain", "port-sudan-sudan", "salal<PERSON>-<PERSON>man", "dar-es-salaam-tanzania", "maputo-mozambique", "nacala-mozambique", "mahajanga-madagascar", "mtunzini-south-africa", "cape-town-south-africa", "port-elizabeth-south-africa", "muanda-congo-dem-rep-", "pointe-noire-congo-rep-", "sao-tome-sao-tome-and-principe", "lynn-ma-united-states", "le-porge-france", "veules-les-roses-france", "duba-saudi-arabia", "mocha-yemen", "shantou-china", "tel-aviv-israel", "mellieha-malta", "rome-italy", "kwa-ibo-nigeria", "luanda-angola", "moroni-comoros", "gran-canaria-canary-islands-spain", "carana-seychelles", "butterworth-malaysia", "aqaba-jordan", "barka-oman", "abu-dhabi-united-arab-emirates", "manama-bahrain", "al-faw-iraq", "kuwait-city-kuwait", "al-khobar-saudi-arabia", "bastia-france", "golfo-aranci-italy", "zahara-de-los-atunes-spain", "torreguadiaro-spain", "nador-morocco", "collo-algeria", "tympaki-greece", "sines-portugal", "hulhumale-maldives", "morib-malaysia", "kulhudhufushi-maldives", "port-nl-france", "petit-perello-france"], "landing_points_in_country": ["penmarch-france", "marseille-france", "toulon-france", "plerin-france", "lannion-france", "cayeux-sur-mer-france", "ajaccio-france", "cannes-france", "lle-rousse-france", "la-seyne-france", "saint-hilair<PERSON>-<PERSON>-riez-france", "surville-france", "bastia-france", "le-porge-france", "veules-les-roses-france", "port-nl-france", "petit-perello-france"]}