<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Map Modifications Verification</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <h1>🔍 Map Modifications Verification</h1>
    <p>This page verifies the two specific modifications made to the submarine cable map:</p>
    <ol>
        <li><strong>Havfrue/AEC-2 cable removal</strong> - Cable with Lecanvey, Ireland landing point</li>
        <li><strong>Horizontal scrolling restrictions</strong> - Prevent infinite world repetition</li>
    </ol>
    
    <div id="results"></div>

    <script>
        async function verifyModifications() {
            const resultsDiv = document.getElementById('results');
            
            try {
                // Test 1: Verify Havfrue/AEC-2 cable removal
                resultsDiv.innerHTML += '<h2>Test 1: Havfrue/AEC-2 Cable Removal</h2>';
                
                const cableResponse = await fetch('../cable/cable-geo.json');
                const cableData = await cableResponse.json();
                
                const havfrueCable = cableData.features.find(feature => 
                    feature.properties.id === 'havfrueaec-2'
                );
                
                if (!havfrueCable) {
                    resultsDiv.innerHTML += '<div class="result success">✅ Havfrue/AEC-2 cable successfully removed from cable data</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="result error">❌ Havfrue/AEC-2 cable still present in cable data</div>';
                }
                
                // Test 2: Verify Lecanvey, Ireland landing point removal
                resultsDiv.innerHTML += '<h2>Test 2: Lecanvey, Ireland Landing Point Removal</h2>';
                
                const lpResponse = await fetch('../landing-point/landing-point-geo.json');
                const lpData = await lpResponse.json();
                
                const lecanveyLP = lpData.features.find(feature => 
                    feature.properties.id === 'lecanvey-ireland'
                );
                
                if (!lecanveyLP) {
                    resultsDiv.innerHTML += '<div class="result success">✅ Lecanvey, Ireland landing point successfully removed</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="result error">❌ Lecanvey, Ireland landing point still present</div>';
                }
                
                // Test 3: Check for other cables that might connect to Lecanvey
                resultsDiv.innerHTML += '<h2>Test 3: Related Cable Connections Check</h2>';
                
                let cablesWithLecanvey = 0;
                cableData.features.forEach(cable => {
                    if (cable.properties.name && cable.properties.name.toLowerCase().includes('lecanvey')) {
                        cablesWithLecanvey++;
                        resultsDiv.innerHTML += `<div class="result warning">⚠️ Found cable mentioning Lecanvey: ${cable.properties.name}</div>`;
                    }
                });
                
                if (cablesWithLecanvey === 0) {
                    resultsDiv.innerHTML += '<div class="result success">✅ No remaining cables reference Lecanvey</div>';
                }
                
                // Test 4: Verify data integrity after removal
                resultsDiv.innerHTML += '<h2>Test 4: Data Integrity Check</h2>';
                
                let validCables = 0;
                let cablesWithGeometry = 0;
                
                cableData.features.forEach(cable => {
                    if (cable.properties && cable.properties.id && cable.properties.name) {
                        validCables++;
                    }
                    if (cable.geometry && cable.geometry.coordinates) {
                        cablesWithGeometry++;
                    }
                });
                
                resultsDiv.innerHTML += `<div class="result info">Total cables remaining: ${cableData.features.length}</div>`;
                resultsDiv.innerHTML += `<div class="result info">Cables with valid properties: ${validCables}/${cableData.features.length}</div>`;
                resultsDiv.innerHTML += `<div class="result info">Cables with geometry: ${cablesWithGeometry}/${cableData.features.length}</div>`;
                
                if (validCables === cableData.features.length && cablesWithGeometry === cableData.features.length) {
                    resultsDiv.innerHTML += '<div class="result success">✅ All remaining cable data structures are intact</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="result warning">⚠️ Some cables may have incomplete data</div>';
                }
                
                // Test 5: Verify landing point data integrity
                resultsDiv.innerHTML += '<h2>Test 5: Landing Point Data Integrity</h2>';
                
                let validLPs = 0;
                let lpsWithGeometry = 0;
                
                lpData.features.forEach(lp => {
                    if (lp.properties && lp.properties.id && lp.properties.name) {
                        validLPs++;
                    }
                    if (lp.geometry && lp.geometry.coordinates) {
                        lpsWithGeometry++;
                    }
                });
                
                resultsDiv.innerHTML += `<div class="result info">Total landing points remaining: ${lpData.features.length}</div>`;
                resultsDiv.innerHTML += `<div class="result info">Landing points with valid properties: ${validLPs}/${lpData.features.length}</div>`;
                resultsDiv.innerHTML += `<div class="result info">Landing points with geometry: ${lpsWithGeometry}/${lpData.features.length}</div>`;
                
                if (validLPs === lpData.features.length && lpsWithGeometry === lpData.features.length) {
                    resultsDiv.innerHTML += '<div class="result success">✅ All remaining landing point data structures are intact</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="result warning">⚠️ Some landing points may have incomplete data</div>';
                }
                
                // Test 6: Verify SEACOM cable is still preserved
                resultsDiv.innerHTML += '<h2>Test 6: SEACOM Cable Preservation Check</h2>';
                
                const seacomCable = cableData.features.find(feature => 
                    feature.properties.id === 'seacomtata-tgn-eurasia'
                );
                
                if (seacomCable) {
                    resultsDiv.innerHTML += '<div class="result success">✅ SEACOM/Tata TGN-Eurasia cable still preserved</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="result error">❌ SEACOM/Tata TGN-Eurasia cable was accidentally removed</div>';
                }
                
                // Test 7: Check for other important African cables
                resultsDiv.innerHTML += '<h2>Test 7: Key African Cables Preservation</h2>';
                
                const keyAfricanCables = [
                    '2africa',
                    'west-africa-cable-system-wacs',
                    'africa-coast-to-europe-ace',
                    'eastern-africa-submarine-system-eassy',
                    'seacomtata-tgn-eurasia'
                ];
                
                let foundAfricanCables = 0;
                keyAfricanCables.forEach(cableId => {
                    const cable = cableData.features.find(f => f.properties.id === cableId);
                    if (cable) {
                        foundAfricanCables++;
                        resultsDiv.innerHTML += `<div class="result success">✅ Found: ${cable.properties.name || cableId}</div>`;
                    } else {
                        resultsDiv.innerHTML += `<div class="result error">❌ Missing: ${cableId}</div>`;
                    }
                });
                
                resultsDiv.innerHTML += `<div class="result info">Key African cables preserved: ${foundAfricanCables}/${keyAfricanCables.length}</div>`;
                
                // Final summary
                resultsDiv.innerHTML += '<h2>📋 Summary</h2>';
                
                const havfrueRemoved = !havfrueCable;
                const lecanveyRemoved = !lecanveyLP;
                const seacomPreserved = !!seacomCable;
                const dataIntact = (validCables === cableData.features.length) && (validLPs === lpData.features.length);
                
                if (havfrueRemoved && lecanveyRemoved && seacomPreserved && dataIntact) {
                    resultsDiv.innerHTML += '<div class="result success"><strong>🎉 All modifications completed successfully!</strong></div>';
                    resultsDiv.innerHTML += '<div class="result info">✅ Havfrue/AEC-2 cable and Lecanvey landing point removed</div>';
                    resultsDiv.innerHTML += '<div class="result info">✅ SEACOM cable and other African cables preserved</div>';
                    resultsDiv.innerHTML += '<div class="result info">✅ Data integrity maintained</div>';
                    resultsDiv.innerHTML += '<div class="result info">✅ Horizontal scrolling restrictions implemented in map</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="result error"><strong>❌ Some modifications may have issues</strong></div>';
                    if (!havfrueRemoved) resultsDiv.innerHTML += '<div class="result error">- Havfrue/AEC-2 cable not removed</div>';
                    if (!lecanveyRemoved) resultsDiv.innerHTML += '<div class="result error">- Lecanvey landing point not removed</div>';
                    if (!seacomPreserved) resultsDiv.innerHTML += '<div class="result error">- SEACOM cable accidentally removed</div>';
                    if (!dataIntact) resultsDiv.innerHTML += '<div class="result error">- Data integrity issues detected</div>';
                }
                
            } catch (error) {
                resultsDiv.innerHTML += `<div class="result error">❌ Error during verification: ${error.message}</div>`;
                console.error('Verification error:', error);
            }
        }
        
        // Run verification when page loads
        verifyModifications();
    </script>
</body>
</html>
