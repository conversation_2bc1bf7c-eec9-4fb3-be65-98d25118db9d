{"id": "santos-brazil", "name": "Santos, Brazil", "country": "Brazil", "cables": [{"id": "junior", "name": "Junior", "rfs_year": 2018, "is_planned": false}, {"id": "monet", "name": "Monet", "rfs_year": 2017, "is_planned": false}, {"id": "south-america-1-sam-1", "name": "South America-1 (SAm-1)", "rfs_year": 2001, "is_planned": false}, {"id": "south-american-crossing-sac", "name": "South American Crossing (SAC)", "rfs_year": 2000, "is_planned": false}, {"id": "tannat", "name": "<PERSON><PERSON>", "rfs_year": 2018, "is_planned": false}], "landing_points": ["st-croix-virgin-islands-u-s-", "valparaso-chile", "fortaleza-brazil", "lurin-peru", "rio-de-janeiro-brazil", "santos-brazil", "las-toninas-argentina", "fort-amador-panama", "boca-raton-fl-united-states", "san-juan-pr-united-states", "arica-chile", "puerto-san-jose-guatemala", "puerto-barrios-guatemala", "salvador-brazil", "puerto-viejo-venezuela", "barranquilla-colombia", "punta-carnero-ecuador", "mancora-peru", "buenaventura-colombia", "maldonado-uruguay", "punta-cana-dominican-republic", "colon-panama"]}