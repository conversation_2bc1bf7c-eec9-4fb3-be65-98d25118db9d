<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Submarine Cable Map - Styling Demo</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0; 
            padding: 0; 
            background-color: #f8f9fa;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        
        .header p {
            margin: 8px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .features-bar {
            background: white;
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .feature-item {
            display: inline-block;
            margin: 0 20px;
            padding: 8px 16px;
            background: #f8f9fa;
            border-radius: 20px;
            font-size: 13px;
            color: #495057;
            border: 1px solid #e9ecef;
        }
        
        .feature-item.highlight {
            background: #e3f2fd;
            border-color: #2e86ab;
            color: #2e86ab;
            font-weight: 600;
        }
        
        #map { 
            height: calc(100vh - 140px);
            width: 100%;
        }
        
        .info {
            padding: 8px 12px;
            font: 14px/16px -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: white;
            background: rgba(255,255,255,0.95);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border-radius: 8px;
            border: 1px solid rgba(0,0,0,0.1);
            min-width: 200px;
        }
        .info h4 {
            margin: 0 0 8px;
            color: #2c3e50;
            font-weight: 600;
        }
        
        /* Professional cable label styling */
        .professional-cable-label {
            background: none !important;
            border: none !important;
            box-shadow: none !important;
        }
        
        .cable-name-display {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(46, 134, 171, 0.6);
            border-radius: 6px;
            padding: 8px 12px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            font-size: 13px;
            font-weight: 600;
            color: #2c3e50;
            text-align: center;
            white-space: nowrap;
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
            pointer-events: none;
            user-select: none;
            position: relative;
            max-width: 300px;
            line-height: 1.4;
        }
        
        .cable-name-display::before {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 6px solid rgba(255, 255, 255, 0.95);
        }
        
        .cable-name-display::after {
            content: '';
            position: absolute;
            bottom: -7px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            border-top: 7px solid rgba(46, 134, 171, 0.6);
        }
        
        .color-palette {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            font-size: 12px;
            z-index: 1000;
            max-width: 200px;
        }
        
        .color-palette h4 {
            margin: 0 0 8px 0;
            color: #2c3e50;
            font-size: 13px;
            font-weight: 600;
        }
        
        .color-sample {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin: 2px;
            border-radius: 3px;
            border: 1px solid rgba(0,0,0,0.1);
        }
        
        .instructions {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            font-size: 12px;
            z-index: 1000;
            max-width: 250px;
        }
        
        .instructions h4 {
            margin: 0 0 8px 0;
            color: #2c3e50;
            font-size: 13px;
            font-weight: 600;
        }
        
        .instructions ul {
            margin: 0;
            padding-left: 16px;
            color: #495057;
        }
        
        .instructions li {
            margin: 4px 0;
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .header h1 { font-size: 24px; }
            .header p { font-size: 14px; }
            .feature-item { margin: 0 8px; font-size: 12px; }
            .cable-name-display { font-size: 12px; padding: 6px 10px; max-width: 250px; }
            .color-palette, .instructions { position: relative; margin: 10px; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌊 Professional Submarine Cable Map</h1>
        <p>Enhanced with Professional Color Scheme & Interactive Cable Labeling</p>
    </div>
    
    <div class="features-bar">
        <div class="feature-item highlight">🎨 Professional Color Palette</div>
        <div class="feature-item">📍 Click-to-Show Cable Names</div>
        <div class="feature-item">🖱️ Enhanced Hover Effects</div>
        <div class="feature-item">📱 Responsive Design</div>
        <div class="feature-item">🌍 World Bounds Restricted</div>
    </div>
    
    <div id="map"></div>
    
    <div class="color-palette">
        <h4>🎨 Professional Colors</h4>
        <div id="color-samples"></div>
        <div style="margin-top: 8px; font-size: 11px; color: #6c757d;">
            40 distinct professional colors<br>
            Optimized for visual clarity
        </div>
    </div>
    
    <div class="instructions">
        <h4>💡 How to Use</h4>
        <ul>
            <li><strong>Click</strong> any cable to show its name</li>
            <li><strong>Hover</strong> for cable details</li>
            <li><strong>Click map</strong> to hide labels</li>
            <li><strong>Zoom</strong> to explore regions</li>
        </ul>
    </div>

    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // Professional color palette
        const professionalColorPalette = [
            '#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#6C757D', '#495057', '#4A90A4', '#8E44AD',
            '#D35400', '#27AE60', '#2C3E50', '#8B4513', '#556B2F', '#4682B4', '#CD853F', '#708090',
            '#2F4F4F', '#800080', '#B22222', '#228B22', '#4169E1', '#DC143C', '#FF8C00', '#9932CC',
            '#8FBC8F', '#483D8B', '#2E8B57', '#B8860B', '#A0522D', '#1E90FF', '#32CD32', '#FF6347',
            '#4B0082', '#DAA520', '#008B8B', '#9400D3', '#FF4500', '#2E8B57', '#8B008B', '#556B2F'
        ];

        // Display color samples
        const colorSamplesDiv = document.getElementById('color-samples');
        professionalColorPalette.slice(0, 20).forEach(color => {
            const sample = document.createElement('div');
            sample.className = 'color-sample';
            sample.style.backgroundColor = color;
            sample.title = color;
            colorSamplesDiv.appendChild(sample);
        });

        // Initialize map
        const map = L.map('map', {
            worldCopyJump: false,
            maxBounds: [[-90, -180], [90, 180]],
            maxBoundsViscosity: 1.0
        }).setView([20, 0], 3);

        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map);

        const cableLayer = L.layerGroup().addTo(map);
        let activeCableLabel = null;

        function getProfessionalCableColor(cableId, index) {
            let hash = 0;
            for (let i = 0; i < cableId.length; i++) {
                const char = cableId.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash;
            }
            const colorIndex = (Math.abs(hash) + index) % professionalColorPalette.length;
            return professionalColorPalette[colorIndex];
        }

        function showCableNameLabel(feature, layer, clickEvent) {
            if (activeCableLabel) {
                map.removeLayer(activeCableLabel);
                activeCableLabel = null;
            }

            const cableName = feature.properties.name || feature.properties.id;
            if (!cableName) return;

            const labelIcon = L.divIcon({
                className: 'professional-cable-label',
                html: `<div class="cable-name-display">${cableName}</div>`,
                iconSize: [null, null],
                iconAnchor: [0, 25]
            });

            activeCableLabel = L.marker(clickEvent.latlng, {
                icon: labelIcon,
                interactive: false,
                zIndexOffset: 2000
            });

            activeCableLabel.addTo(map);

            setTimeout(() => {
                if (activeCableLabel) {
                    map.removeLayer(activeCableLabel);
                    activeCableLabel = null;
                }
            }, 8000);
        }

        function hideCableLabel() {
            if (activeCableLabel) {
                map.removeLayer(activeCableLabel);
                activeCableLabel = null;
            }
        }

        const info = L.control();
        info.onAdd = function(map) {
            this._div = L.DomUtil.create('div', 'info');
            this.update();
            return this._div;
        };
        info.update = function(props) {
            this._div.innerHTML = '<h4>🌊 Cable Information</h4>' +
                (props ?
                    '<b>' + props.name + '</b><br/>' +
                    (props.rfs ? 'Ready for Service: ' + props.rfs + '<br/>' : '') +
                    (props.length ? 'Length: ' + props.length + ' km<br/>' : '') +
                    (props.owners ? 'Owners: ' + props.owners + '<br/>' : '') +
                    '<br/><i style="color: #6c757d;">Click to show name label</i>'
                    : 'Hover over a cable for details');
        };
        info.addTo(map);

        // Load and display cables
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                // Show first 30 cables for demo
                const demoFeatures = data.features.slice(0, 30);
                const demoData = { ...data, features: demoFeatures };

                L.geoJSON(demoData, {
                    style: function(feature) {
                        const cableIndex = demoFeatures.indexOf(feature);
                        const cableColor = getProfessionalCableColor(feature.properties.id, cableIndex);
                        
                        return {
                            color: cableColor,
                            weight: 2.5,
                            opacity: 0.85,
                            dashArray: feature.properties.is_planned ? '8, 4' : null
                        };
                    },
                    onEachFeature: function(feature, layer) {
                        const cableIndex = demoFeatures.indexOf(feature);
                        const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);
                        
                        layer.on({
                            mouseover: function(e) {
                                layer.setStyle({ weight: 4, opacity: 1 });
                                info.update(feature.properties);
                            },
                            mouseout: function(e) {
                                layer.setStyle({ weight: 2.5, opacity: 0.85 });
                                info.update();
                            },
                            click: function(e) {
                                showCableNameLabel(feature, layer, e);
                                L.DomEvent.stopPropagation(e);
                            }
                        });

                        let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                        popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50;">' + feature.properties.name + '</h4>';
                        popupContent += '<div style="display: flex; align-items: center; margin-bottom: 6px;">';
                        popupContent += '<span style="color: ' + originalColor + '; font-size: 16px; margin-right: 6px;">●</span>';
                        popupContent += '<span style="font-size: 12px; color: #6c757d;">Professional Color</span></div>';
                        if (feature.properties.length) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                        }
                        popupContent += '</div>';
                        
                        layer.bindPopup(popupContent);
                    }
                }).addTo(cableLayer);
            });

        map.on('click', hideCableLabel);
        L.control.scale().addTo(map);
    </script>
</body>
</html>
