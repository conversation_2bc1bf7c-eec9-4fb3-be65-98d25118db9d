<!DOCTYPE html>
<html>
<head>
    <title>Map View Checker - African Cables</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body { margin: 0; font-family: Arial, sans-serif; }
        #map { height: 70vh; width: 100%; }
        .info-panel { padding: 20px; background-color: #f8f9fa; }
        .button-group { margin: 10px 0; }
        .btn { padding: 8px 16px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background-color: #0056b3; }
        .status { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="info-panel">
        <h2>🗺️ Map View Checker - African Submarine Cables</h2>
        <p>This tool helps verify if African cables are visible by testing different map views and zoom levels.</p>
        
        <div class="button-group">
            <button class="btn" onclick="goToAfrica()">🌍 Focus on Africa</button>
            <button class="btn" onclick="goToSouthAfrica()">🇿🇦 Focus on South Africa</button>
            <button class="btn" onclick="goToWestAfrica()">🌊 Focus on West Africa</button>
            <button class="btn" onclick="goToEastAfrica()">🏖️ Focus on East Africa</button>
            <button class="btn" onclick="goToGlobal()">🌐 Global View</button>
        </div>
        
        <div id="status" class="status info">
            <strong>Status:</strong> Loading map and cable data...
        </div>
        
        <div id="cable-count" class="status">
            <strong>Cables loaded:</strong> <span id="count">0</span> | 
            <strong>African cables:</strong> <span id="african-count">0</span>
        </div>
    </div>

    <div id="map"></div>

    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        // Initialize map centered on Africa
        const map = L.map('map').setView([0, 20], 3);

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        // Status elements
        const statusDiv = document.getElementById('status');
        const countSpan = document.getElementById('count');
        const africanCountSpan = document.getElementById('african-count');

        // Cable layer
        let cableLayer = L.layerGroup().addTo(map);
        let africanCables = [];
        let allCables = [];

        // Navigation functions
        function goToAfrica() {
            map.setView([0, 20], 3);
            updateStatus('Focused on Africa (0°, 20°) - Zoom level 3');
        }

        function goToSouthAfrica() {
            map.setView([-30, 25], 5);
            updateStatus('Focused on South Africa (-30°, 25°) - Zoom level 5');
        }

        function goToWestAfrica() {
            map.setView([5, -5], 4);
            updateStatus('Focused on West Africa (5°, -5°) - Zoom level 4');
        }

        function goToEastAfrica() {
            map.setView([-5, 40], 4);
            updateStatus('Focused on East Africa (-5°, 40°) - Zoom level 4');
        }

        function goToGlobal() {
            map.setView([20, 0], 2);
            updateStatus('Global view (20°, 0°) - Zoom level 2');
        }

        function updateStatus(message) {
            statusDiv.innerHTML = `<strong>Status:</strong> ${message}`;
            statusDiv.className = 'status info';
        }

        function updateError(message) {
            statusDiv.innerHTML = `<strong>Error:</strong> ${message}`;
            statusDiv.className = 'status error';
        }

        function updateSuccess(message) {
            statusDiv.innerHTML = `<strong>Success:</strong> ${message}`;
            statusDiv.className = 'status success';
        }

        // African cable detection
        function isAfricanCable(cable) {
            const name = cable.properties.name.toLowerCase();
            const id = cable.properties.id.toLowerCase();
            
            return name.includes('africa') || 
                   name.includes('wacs') || 
                   name.includes('ace') || 
                   name.includes('safe') || 
                   name.includes('eassy') ||
                   id.includes('africa') ||
                   id.includes('wacs') ||
                   id.includes('ace') ||
                   id.includes('safe') ||
                   id.includes('eassy');
        }

        // Load and display cables
        fetch('../cable/cable-geo.json')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                allCables = data.features;
                africanCables = data.features.filter(isAfricanCable);
                
                countSpan.textContent = allCables.length;
                africanCountSpan.textContent = africanCables.length;

                // Add all cables to map
                data.features.forEach(feature => {
                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAfrican = isAfricanCable(feature);
                        
                        const cable = L.geoJSON(feature, {
                            style: {
                                color: isAfrican ? '#ff0000' : '#0066cc', // Red for African, blue for others
                                weight: isAfrican ? 3 : 1,
                                opacity: isAfrican ? 0.8 : 0.5
                            }
                        });

                        cable.bindPopup(`
                            <strong>${feature.properties.name}</strong><br>
                            ID: ${feature.properties.id}<br>
                            Length: ${feature.properties.length || 'N/A'}<br>
                            RFS: ${feature.properties.rfs || 'N/A'}<br>
                            <strong>African Cable: ${isAfrican ? 'YES' : 'No'}</strong>
                        `);

                        cableLayer.addLayer(cable);
                    }
                });

                if (africanCables.length > 0) {
                    updateSuccess(`Loaded ${allCables.length} cables (${africanCables.length} African). African cables shown in RED.`);
                    
                    // Focus on first African cable if available
                    const firstAfrican = africanCables[0];
                    if (firstAfrican && firstAfrican.geometry && firstAfrican.geometry.coordinates) {
                        console.log('First African cable:', firstAfrican.properties.name);
                        console.log('Coordinates:', firstAfrican.geometry.coordinates);
                    }
                } else {
                    updateError(`Loaded ${allCables.length} cables but found NO African cables!`);
                }
            })
            .catch(error => {
                updateError(`Failed to load cable data: ${error.message}`);
                console.error('Cable loading error:', error);
            });

        // Add legend
        const legend = L.control({position: 'bottomright'});
        legend.onAdd = function (map) {
            const div = L.DomUtil.create('div', 'info legend');
            div.innerHTML = `
                <div style="background: white; padding: 10px; border-radius: 5px; box-shadow: 0 0 15px rgba(0,0,0,0.2);">
                    <strong>Legend</strong><br>
                    <span style="color: #ff0000; font-weight: bold;">━━━</span> African Cables<br>
                    <span style="color: #0066cc;">━━━</span> Other Cables
                </div>
            `;
            return div;
        };
        legend.addTo(map);

        // Initial status
        updateStatus('Map initialized - Loading cable data...');
    </script>
</body>
</html>
