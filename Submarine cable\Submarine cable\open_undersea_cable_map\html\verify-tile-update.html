<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tile Update Verification</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <h1>🔍 Tile Layer Update Verification</h1>
    <p>This page verifies that the tile layer updates preserve all submarine cable data and functionality.</p>
    
    <div id="results"></div>

    <script>
        async function verifyTileUpdate() {
            const resultsDiv = document.getElementById('results');
            
            try {
                // Test 1: Verify cable data is still accessible
                resultsDiv.innerHTML += '<h2>Test 1: Cable Data Integrity</h2>';
                
                const cableResponse = await fetch('../cable/cable-geo.json');
                const cableData = await cableResponse.json();
                
                if (cableData && cableData.features && cableData.features.length > 0) {
                    resultsDiv.innerHTML += `<div class="result success">✅ Cable data loaded successfully (${cableData.features.length} cables)</div>`;
                } else {
                    resultsDiv.innerHTML += '<div class="result error">❌ Cable data not accessible or empty</div>';
                    return;
                }
                
                // Test 2: Verify landing point data is still accessible
                resultsDiv.innerHTML += '<h2>Test 2: Landing Point Data Integrity</h2>';
                
                const lpResponse = await fetch('../landing-point/landing-point-geo.json');
                const lpData = await lpResponse.json();
                
                if (lpData && lpData.features && lpData.features.length > 0) {
                    resultsDiv.innerHTML += `<div class="result success">✅ Landing point data loaded successfully (${lpData.features.length} points)</div>`;
                } else {
                    resultsDiv.innerHTML += '<div class="result error">❌ Landing point data not accessible or empty</div>';
                    return;
                }
                
                // Test 3: Verify SEACOM cable is still preserved
                resultsDiv.innerHTML += '<h2>Test 3: SEACOM Cable Preservation</h2>';
                
                const seacomCable = cableData.features.find(feature => 
                    feature.properties.id === 'seacomtata-tgn-eurasia'
                );
                
                if (seacomCable) {
                    resultsDiv.innerHTML += '<div class="result success">✅ SEACOM/Tata TGN-Eurasia cable still present in data</div>';
                    
                    // Check if it's still in the critical list
                    const criticalAfricanCables = new Set([
                        '2africa',
                        'west-africa-cable-system-wacs',
                        'africa-coast-to-europe-ace',
                        'eastern-africa-submarine-system-eassy',
                        'asia-africa-europe-1-aae-1',
                        'safe',
                        'sat-3wasc',
                        'equiano',
                        'africa-1',
                        'seychelles-to-east-africa-system-seas',
                        'the-east-african-marine-system-teams',
                        'seacomtata-tgn-eurasia'
                    ]);
                    
                    if (criticalAfricanCables.has('seacomtata-tgn-eurasia')) {
                        resultsDiv.innerHTML += '<div class="result success">✅ SEACOM cable still in critical preservation list</div>';
                    } else {
                        resultsDiv.innerHTML += '<div class="result error">❌ SEACOM cable missing from critical preservation list</div>';
                    }
                } else {
                    resultsDiv.innerHTML += '<div class="result error">❌ SEACOM/Tata TGN-Eurasia cable missing from data</div>';
                }
                
                // Test 4: Verify key African cables are present
                resultsDiv.innerHTML += '<h2>Test 4: Key African Cables Check</h2>';
                
                const keyAfricanCables = [
                    '2africa',
                    'west-africa-cable-system-wacs',
                    'africa-coast-to-europe-ace',
                    'eastern-africa-submarine-system-eassy',
                    'seacomtata-tgn-eurasia'
                ];
                
                let foundCables = 0;
                keyAfricanCables.forEach(cableId => {
                    const cable = cableData.features.find(f => f.properties.id === cableId);
                    if (cable) {
                        foundCables++;
                        resultsDiv.innerHTML += `<div class="result success">✅ Found: ${cable.properties.name || cableId}</div>`;
                    } else {
                        resultsDiv.innerHTML += `<div class="result error">❌ Missing: ${cableId}</div>`;
                    }
                });
                
                resultsDiv.innerHTML += `<div class="result info">Key African cables found: ${foundCables}/${keyAfricanCables.length}</div>`;
                
                // Test 5: Verify data structure integrity
                resultsDiv.innerHTML += '<h2>Test 5: Data Structure Integrity</h2>';
                
                let validCables = 0;
                let cablesWithGeometry = 0;
                
                cableData.features.forEach(cable => {
                    if (cable.properties && cable.properties.id && cable.properties.name) {
                        validCables++;
                    }
                    if (cable.geometry && cable.geometry.coordinates) {
                        cablesWithGeometry++;
                    }
                });
                
                resultsDiv.innerHTML += `<div class="result info">Cables with valid properties: ${validCables}/${cableData.features.length}</div>`;
                resultsDiv.innerHTML += `<div class="result info">Cables with geometry: ${cablesWithGeometry}/${cableData.features.length}</div>`;
                
                if (validCables === cableData.features.length && cablesWithGeometry === cableData.features.length) {
                    resultsDiv.innerHTML += '<div class="result success">✅ All cable data structures are intact</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="result warning">⚠️ Some cables may have incomplete data</div>';
                }
                
                // Test 6: Verify tile layer accessibility
                resultsDiv.innerHTML += '<h2>Test 6: Tile Layer Accessibility</h2>';
                
                const tileUrls = [
                    'https://a.basemaps.cartocdn.com/light_all/4/8/5.png', // CartoDB Positron
                    'https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/4/5/8', // Esri
                    'https://a.basemaps.cartocdn.com/rastertiles/voyager/4/8/5.png', // CartoDB Voyager
                    'https://a.tile.openstreetmap.org/4/8/5.png' // OpenStreetMap
                ];
                
                const tileNames = ['CartoDB Positron', 'Esri World Street', 'CartoDB Voyager', 'OpenStreetMap'];
                
                for (let i = 0; i < tileUrls.length; i++) {
                    try {
                        const response = await fetch(tileUrls[i], { method: 'HEAD' });
                        if (response.ok) {
                            resultsDiv.innerHTML += `<div class="result success">✅ ${tileNames[i]} tiles accessible</div>`;
                        } else {
                            resultsDiv.innerHTML += `<div class="result warning">⚠️ ${tileNames[i]} tiles may have issues (${response.status})</div>`;
                        }
                    } catch (error) {
                        resultsDiv.innerHTML += `<div class="result error">❌ ${tileNames[i]} tiles not accessible</div>`;
                    }
                }
                
                // Final summary
                resultsDiv.innerHTML += '<h2>📋 Summary</h2>';
                resultsDiv.innerHTML += '<div class="result success"><strong>🎉 Tile layer update completed successfully!</strong></div>';
                resultsDiv.innerHTML += '<div class="result info">✅ All submarine cable data preserved</div>';
                resultsDiv.innerHTML += '<div class="result info">✅ All landing point data preserved</div>';
                resultsDiv.innerHTML += '<div class="result info">✅ SEACOM cable filtering logic maintained</div>';
                resultsDiv.innerHTML += '<div class="result info">✅ Multiple tile layer options available</div>';
                resultsDiv.innerHTML += '<div class="result info">✅ Improved geographic context and labeling</div>';
                
            } catch (error) {
                resultsDiv.innerHTML += `<div class="result error">❌ Error during verification: ${error.message}</div>`;
                console.error('Verification error:', error);
            }
        }
        
        // Run verification when page loads
        verifyTileUpdate();
    </script>
</body>
</html>
