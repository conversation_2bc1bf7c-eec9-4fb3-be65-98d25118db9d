{"id": "portugal", "name": "Portugal", "cables": [{"id": "2africa", "name": "2Africa", "rfs_year": 2023, "is_planned": true}, {"id": "africa-coast-to-europe-ace", "name": "Africa Coast to Europe (ACE)", "rfs_year": 2012, "is_planned": false}, {"id": "azores-fiber-optic-system-afos", "name": "Azores Fiber Optic System (AFOS)", "rfs_year": 1998, "is_planned": false}, {"id": "bugio", "name": "BUGIO", "rfs_year": 1996, "is_planned": false}, {"id": "cam-ring", "name": "CAM Ring", "rfs_year": 2003, "is_planned": false}, {"id": "columbus-iii-azores-portugal", "name": "Columbus-III Azores-Portugal", "rfs_year": 1999, "is_planned": false}, {"id": "continente-madeira", "name": "Continente-Madeira", "rfs_year": 2000, "is_planned": false}, {"id": "ellalink", "name": "EllaLink", "rfs_year": 2021, "is_planned": false}, {"id": "equiano", "name": "Equiano", "rfs_year": 2022, "is_planned": true}, {"id": "europe-india-gateway-eig", "name": "Europe India Gateway (EIG)", "rfs_year": 2011, "is_planned": false}, {"id": "flores-corvo-cable-system", "name": "Flores-Corvo Cable System", "rfs_year": 2014, "is_planned": false}, {"id": "mainone", "name": "MainOne", "rfs_year": 2010, "is_planned": false}, {"id": "medusa-submarine-cable-system", "name": "Medusa Submarine Cable System", "rfs_year": 2024, "is_planned": true}, {"id": "sagres", "name": "Sagres", "rfs_year": 1998, "is_planned": false}, {"id": "sat-3wasc", "name": "SAT-3/WASC", "rfs_year": 2002, "is_planned": false}, {"id": "seamewe-3", "name": "SeaMeWe-3", "rfs_year": 1999, "is_planned": false}, {"id": "sines-to-lisbon", "name": "Sines to Lisbon", "rfs_year": 2025, "is_planned": true}, {"id": "tata-tgn-western-europe", "name": "Tata TGN-Western Europe", "rfs_year": 2002, "is_planned": false}, {"id": "west-africa-cable-system-wacs", "name": "West Africa Cable System (WACS)", "rfs_year": 2012, "is_planned": false}], "landing_points": ["deep-water-bay-china", "taipa-china", "batangas-philippines", "danang-vietnam", "tungku-brunei", "mersing-malaysia", "tuas-singapore", "okinawa-japan", "shanghai-china", "toucheng-taiwan", "fangshan-taiwan", "ancol-indonesia", "perth-wa-australia", "penang-malaysia", "satun-thailand", "pyapon-myanmar", "mt-lavinia-sri-lanka", "cochin-india", "mumbai-india", "karachi-pakistan", "muscat-oman", "fujairah-united-arab-emirates", "djibouti-city-djibouti", "suez-egypt", "alexandria-egypt", "yeroskipos-cyprus", "marmaris-turkey", "mazara-del-vallo-italy", "sesimbra-portugal", "penmarch-france", "goonhilly-downs-united-kingdom", "medan-indonesia", "jeddah-saudi-arabia", "alta-vista-canary-islands-spain", "dakar-senegal", "accra-ghana", "cotonou-benin", "lagos-nigeria", "libreville-gabon", "cacuaco-angola", "melkbosstrand-south-africa", "douala-cameroon", "abidjan-cte-divoire", "seixal-portugal", "chania-greece", "highbridge-united-kingdom", "bilbao-spain", "ttouan-morocco", "ostend-belgium", "barka-oman", "monaco-monaco", "tripoli-libya", "haramous-djibouti", "bude-united-kingdom", "gibraltar-gibraltar", "nouakchott-mauritania", "banjul-gambia", "conakry-guinea", "freetown-sierra-leone", "monrovia-liberia", "muanda-congo-dem-rep-", "pointe-noire-congo-rep-", "lome-togo", "bata-equatorial-guinea", "yzerfontein-south-africa", "swakopmund-namibia", "limbe-cameroon", "zafarana-egypt", "abu-talat-egypt", "kribi-cameroon", "fortaleza-brazil", "el-goro-canary-islands-spain", "sangano-angola", "flores-portugal", "corvo-portugal", "graciosa-portugal", "faial-portugal", "funchal-portugal", "ponta-delgada-portugal", "porto-santo-portugal", "velas-portugal", "sao-mateus-portugal", "vila-do-porto-portugal", "angra-do-heroismo-portugal", "sines-portugal", "duynefontein-south-africa", "carcavelos-portugal", "praia-cape-verde", "suro-guinea-bissau", "chipiona-spain", "granadilla-canary-islands-spain", "geoje-south-korea", "ruperts-bay-saint-helena-ascension-and-tristan-da-cunha", "ras-ghareb-egypt", "port-said-egypt", "genoa-italy", "marseille-france", "barcelona-spain", "port-sudan-sudan", "salal<PERSON>-<PERSON>man", "mogadishu-somalia", "mombasa-kenya", "dar-es-salaam-tanzania", "maputo-mozambique", "nacala-mozambique", "mahajanga-madagascar", "mtunzini-south-africa", "cape-town-south-africa", "port-elizabeth-south-africa", "yanbu-saudi-arabia", "sao-tome-sao-tome-and-principe", "shantou-china", "kwa-ibo-nigeria", "luanda-angola", "moroni-comoros", "gran-canaria-canary-islands-spain", "carana-seychelles", "abu-dhabi-united-arab-emirates", "kalba-united-arab-emirates", "doha-qatar", "manama-bahrain", "al-faw-iraq", "kuwait-city-kuwait", "al-khobar-saudi-arabia", "duba-saudi-arabia", "zahara-de-los-atunes-spain", "torreguadiaro-spain", "nador-morocco", "algiers-algeria", "collo-algeria", "tympaki-greece", "berbera-somalia", "bizerte-tunisia", "burgau-portugal"], "landing_points_in_country": ["sesimbra-portugal", "funchal-portugal", "ponta-delgada-portugal", "porto-santo-portugal", "seixal-portugal", "sines-portugal", "carcavelos-portugal", "flores-portugal", "corvo-portugal", "graciosa-portugal", "faial-portugal", "velas-portugal", "sao-mateus-portugal", "vila-do-porto-portugal", "angra-do-heroismo-portugal", "burgau-portugal"]}