{"id": "singapore", "name": "Singapore", "cables": [{"id": "apcn-2", "name": "APCN-2", "rfs_year": 2001, "is_planned": false}, {"id": "apricot", "name": "Apricot", "rfs_year": 2024, "is_planned": true}, {"id": "asia-america-gateway-aag-cable-system", "name": "Asia-America Gateway (AAG) Cable System", "rfs_year": 2009, "is_planned": false}, {"id": "asia-connect-cable-1-acc-1", "name": "Asia Connect Cable-1 (ACC-1)", "rfs_year": 2024, "is_planned": true}, {"id": "asia-direct-cable-adc", "name": "Asia Direct Cable (ADC)", "rfs_year": 2023, "is_planned": true}, {"id": "asia-pacific-gateway-apg", "name": "Asia Pacific Gateway (APG)", "rfs_year": 2016, "is_planned": false}, {"id": "asia-submarine-cable-express-asecahaya-malaysia", "name": "Asia Submarine-cable Express (ASE)/Cahaya Malaysia", "rfs_year": 2012, "is_planned": false}, {"id": "australia-singapore-cable-asc", "name": "Australia-Singapore Cable (ASC)", "rfs_year": 2018, "is_planned": false}, {"id": "b2js-jakarta-bangka-batam-singapore-cable-system", "name": "B2JS (Jakarta-Bangka-Batam-Singapore) Cable System", "rfs_year": 2013, "is_planned": false}, {"id": "batam-singapore-cable-system-bscs", "name": "Batam Singapore Cable System (BSCS)", "rfs_year": 2009, "is_planned": false}, {"id": "bifrost", "name": "Bifrost", "rfs_year": 2024, "is_planned": true}, {"id": "eac-c2c", "name": "EAC-C2C", "rfs_year": 2002, "is_planned": false}, {"id": "echo", "name": "Echo", "rfs_year": 2023, "is_planned": true}, {"id": "hawaiki-nui", "name": "<PERSON><PERSON><PERSON>", "rfs_year": 2025, "is_planned": true}, {"id": "i2i-cable-network-i2icn", "name": "i2i Cable Network (i2icn)", "rfs_year": 2002, "is_planned": false}, {"id": "india-asia-xpress-iax", "name": "India Asia Xpress (IAX)", "rfs_year": 2023, "is_planned": true}, {"id": "indigo-west", "name": "INDIGO-West", "rfs_year": 2019, "is_planned": false}, {"id": "indonesia-global-gateway-igg-system", "name": "Indonesia Global Gateway (IGG) System", "rfs_year": 2018, "is_planned": false}, {"id": "j<PERSON><PERSON><PERSON>", "name": "JAKABARE", "rfs_year": 2009, "is_planned": false}, {"id": "jakarta-bangka-bintan-batam-singapore-b3js", "name": "Jakarta-Bangka-Bintan-Batam-Singapore (B3JS)", "rfs_year": 2012, "is_planned": false}, {"id": "matrix-cable-system", "name": "Matrix Cable System", "rfs_year": 2008, "is_planned": false}, {"id": "mist", "name": "MIST", "rfs_year": 2023, "is_planned": true}, {"id": "moratelindo-international-cable-system-1-mic-1", "name": "Moratelindo International Cable System-1 (MIC-1)", "rfs_year": 2008, "is_planned": false}, {"id": "peace-cable", "name": "PEACE Cable", "rfs_year": 2022, "is_planned": false}, {"id": "pgascom", "name": "PGASCOM", "rfs_year": 2010, "is_planned": false}, {"id": "sea-h2x", "name": "SEA-H2X", "rfs_year": 2024, "is_planned": true}, {"id": "seamewe-3", "name": "SeaMeWe-3", "rfs_year": 1999, "is_planned": false}, {"id": "seamewe-4", "name": "SeaMeWe-4", "rfs_year": 2005, "is_planned": false}, {"id": "seamewe-5", "name": "SeaMeWe-5", "rfs_year": 2016, "is_planned": false}, {"id": "seamewe-6", "name": "SeaMeWe-6", "rfs_year": 2025, "is_planned": true}, {"id": "seax-1", "name": "SEAX-1", "rfs_year": 2018, "is_planned": false}, {"id": "singapore-myanmar-sigmar", "name": "Singapore-Myanmar (SIGMAR)", "rfs_year": 2023, "is_planned": true}, {"id": "southeast-asia-japan-cable-2-sjc2", "name": "Southeast Asia-Japan Cable 2 (SJC2)", "rfs_year": 2023, "is_planned": true}, {"id": "southeast-asia-japan-cable-sjc", "name": "Southeast Asia-Japan Cable (SJC)", "rfs_year": 2013, "is_planned": false}, {"id": "tata-tgn-intra-asia-tgn-ia", "name": "Tata TGN-Intra Asia (TGN-IA)", "rfs_year": 2009, "is_planned": false}, {"id": "tata-tgn-tata-indicom", "name": "Tata TGN-Tata Indicom", "rfs_year": 2004, "is_planned": false}, {"id": "thailand-indonesia-singapore-tis", "name": "Thailand-Indonesia-Singapore (TIS)", "rfs_year": 2003, "is_planned": false}], "landing_points": ["deep-water-bay-china", "taipa-china", "batangas-philippines", "danang-vietnam", "tungku-brunei", "mersing-malaysia", "tuas-singapore", "okinawa-japan", "shanghai-china", "toucheng-taiwan", "fangshan-taiwan", "ancol-indonesia", "perth-wa-australia", "penang-malaysia", "satun-thailand", "pyapon-myanmar", "mt-lavinia-sri-lanka", "cochin-india", "mumbai-india", "karachi-pakistan", "muscat-oman", "fujairah-united-arab-emirates", "djibouti-city-djibouti", "suez-egypt", "alexandria-egypt", "yeroskipos-cyprus", "marmaris-turkey", "mazara-del-vallo-italy", "sesimbra-portugal", "penmarch-france", "goonhilly-downs-united-kingdom", "medan-indonesia", "jeddah-saudi-arabia", "chikura-japan", "busan-south-korea", "shantou-china", "kuantan-malaysia", "kitaibaraki-japan", "chennai-india", "lantau-island-china", "chongming-china", "katong-singapore", "tanshui-taiwan", "chania-greece", "ttouan-morocco", "songkhla-thailand", "batam-indonesia", "ostend-belgium", "colombo-sri-lanka", "palermo-italy", "bizerte-tunisia", "annaba-algeria", "marseille-france", "coxs-bazar-bangladesh", "melaka-malaysia", "jakarta-indonesia", "sriracha-thailand", "vung-tau-vietnam", "san-luis-obispo-ca-united-states", "ballesteros-philippines", "keawaula-hi-united-states", "tanguisson-point-guam", "ajigaura-japan", "shima-japan", "cavite-philippines", "pa-li-taiwan", "shindu-ri-south-korea", "tseung-kwan-o-china", "qingdao-china", "chung-hom-kok-china", "changi-south-singapore", "sungai-kakap-indonesia", "tanjung-pakis-indonesia", "tanjung-bemban-indonesia", "nasugbu-philippines", "kuala-tungkal-indonesia", "sakra-island-singapore", "telisai-brunei", "la-union-philippines", "daet-philippines", "maruyama-japan", "nanhui-china", "changi-north-singapore", "toulon-france", "catania-italy", "abu-talat-egypt", "zafarana-egypt", "yanbu-saudi-arabia", "haramous-djibouti", "qalhat-oman", "ngwe-saung-myanmar", "kuakata-bangladesh", "al-hudaydah-yemen", "matara-sri-lanka", "pesaren-indonesia", "batu-prahu-indonesia", "dumai-indonesia", "bali-indonesia", "makassar-indonesia", "balikpapan-indonesia", "tarakan-indonesia", "manado-indonesia", "madura-indonesia", "tanah-merah-singapore", "anyer-indonesia", "flying-fish-cove-christmas-island", "gwadar-pakistan", "mombasa-kenya", "lingang-china", "quy-nhon-vietnam", "victoria-seychelles", "than<PERSON><PERSON>-myanmar", "bosaso-somalia", "mogadishu-somalia", "kismayo-somalia", "hobyo-somalia", "komesu-japan", "letkokkon-myanmar", "geoje-south-korea", "eureka-ca-united-states", "morib-malaysia", "bintan-indonesia", "singapore-singapore", "piti-guam", "agat-guam", "ngeremlengui-palau", "davao-philippines", "mellieha-malta", "minamiboso-japan", "baler-philippines", "darwin-nt-australia", "brisbane-qld-australia", "sydney-nsw-australia", "melbourne-vic-australia", "invercargill-new-zealand", "dunedin-new-zealand", "christchurch-new-zealand", "los-angeles-ca-united-states", "dili-timor-leste", "port-said-egypt", "ras-ghareb-egypt", "hulhumale-maldives", "kapolei-hi-united-states", "kawaihae-hi-united-states", "kulhudhufushi-maldives", "kuching-malaysia", "lingshui-china", "rosarito-mexico", "grover-beach-ca-united-states", "winema-road-beach-or-united-states"], "landing_points_in_country": ["singapore-singapore", "changi-south-singapore", "tuas-singapore", "katong-singapore", "changi-north-singapore", "tanah-merah-singapore", "sakra-island-singapore"]}