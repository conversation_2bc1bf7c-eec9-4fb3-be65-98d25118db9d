<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Map Tile Comparison</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f5f5f5;
        }
        h1 { 
            text-align: center; 
            color: #333; 
            margin-bottom: 30px;
        }
        .description {
            max-width: 800px;
            margin: 0 auto 30px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .map-container { 
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .map-section {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .map-section h3 {
            margin-top: 0;
            color: #333;
            text-align: center;
        }
        .map { 
            height: 300px; 
            width: 100%;
            border-radius: 4px;
        }
        .features {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        .features ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        .recommended {
            border: 2px solid #28a745;
            background-color: #f8fff9;
        }
        .recommended h3 {
            color: #28a745;
        }
        .recommended::before {
            content: "⭐ RECOMMENDED";
            display: block;
            text-align: center;
            background: #28a745;
            color: white;
            padding: 5px;
            margin: -15px -15px 15px -15px;
            border-radius: 6px 6px 0 0;
            font-weight: bold;
            font-size: 12px;
        }
        @media (max-width: 768px) {
            .map-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h1>🗺️ Map Tile Layer Comparison</h1>
    
    <div class="description">
        <h2>Improved Geographic Context for Submarine Cable Map</h2>
        <p>This comparison shows different map tile options for the submarine cable map. The goal is to provide better geographic context and clearer country/region labeling while maintaining all submarine cable data and functionality.</p>
        <p><strong>Focus Areas:</strong> Clear country names, state/province labels, major cities, and good contrast for readability.</p>
    </div>

    <div class="map-container">
        <div class="map-section recommended">
            <h3>CartoDB Positron</h3>
            <div id="map1" class="map"></div>
            <div class="features">
                <strong>Best for submarine cables:</strong>
                <ul>
                    <li>Clean, minimal design with excellent contrast</li>
                    <li>Clear country and city labels at all zoom levels</li>
                    <li>Light background makes cable routes highly visible</li>
                    <li>Professional appearance</li>
                </ul>
            </div>
        </div>

        <div class="map-section">
            <h3>Esri World Street Map</h3>
            <div id="map2" class="map"></div>
            <div class="features">
                <strong>Features:</strong>
                <ul>
                    <li>Detailed street-level information</li>
                    <li>Comprehensive labeling of places</li>
                    <li>Good for detailed geographic context</li>
                    <li>May be too detailed for cable overview</li>
                </ul>
            </div>
        </div>

        <div class="map-section">
            <h3>CartoDB Voyager</h3>
            <div id="map3" class="map"></div>
            <div class="features">
                <strong>Features:</strong>
                <ul>
                    <li>Balanced detail and simplicity</li>
                    <li>Good country and region labeling</li>
                    <li>Subtle terrain features</li>
                    <li>Nice alternative to Positron</li>
                </ul>
            </div>
        </div>

        <div class="map-section">
            <h3>Original OpenStreetMap</h3>
            <div id="map4" class="map"></div>
            <div class="features">
                <strong>Features:</strong>
                <ul>
                    <li>Standard OSM appearance</li>
                    <li>Detailed but can be cluttered</li>
                    <li>Good labeling but less contrast</li>
                    <li>Previous default option</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Initialize all maps with the same view (centered on Africa/Indian Ocean for submarine cables)
        const initialView = [-10, 40]; // East Africa region
        const initialZoom = 4;

        // CartoDB Positron (Recommended)
        const map1 = L.map('map1', {
            worldCopyJump: false,
            maxBounds: [[-90, -180], [90, 180]],
            maxBoundsViscosity: 1.0
        }).setView(initialView, initialZoom);
        L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map1);

        // Esri World Street Map
        const map2 = L.map('map2', {
            worldCopyJump: false,
            maxBounds: [[-90, -180], [90, 180]],
            maxBoundsViscosity: 1.0
        }).setView(initialView, initialZoom);
        L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/{z}/{y}/{x}', {
            attribution: 'Tiles © Esri — Source: Esri, DeLorme, NAVTEQ, USGS, Intermap, iPC, NRCAN, Esri Japan, METI, Esri China (Hong Kong), Esri (Thailand), TomTom, 2012',
            maxZoom: 19
        }).addTo(map2);

        // CartoDB Voyager
        const map3 = L.map('map3', {
            worldCopyJump: false,
            maxBounds: [[-90, -180], [90, 180]],
            maxBoundsViscosity: 1.0
        }).setView(initialView, initialZoom);
        L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map3);

        // Original OpenStreetMap
        const map4 = L.map('map4', {
            worldCopyJump: false,
            maxBounds: [[-90, -180], [90, 180]],
            maxBoundsViscosity: 1.0
        }).setView(initialView, initialZoom);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: 19
        }).addTo(map4);

        // Add sample cable line to show contrast (simulating SEACOM route)
        const sampleCableCoords = [
            [-26.2, 28.0], // South Africa
            [-4.0, 39.7],  // Kenya
            [11.6, 43.1],  // Djibouti
            [19.0, 72.8]   // India
        ];

        const cableStyle = {
            color: '#ff0000',
            weight: 3,
            opacity: 0.8
        };

        // Add the sample cable to all maps
        L.polyline(sampleCableCoords, cableStyle).addTo(map1);
        L.polyline(sampleCableCoords, cableStyle).addTo(map2);
        L.polyline(sampleCableCoords, cableStyle).addTo(map3);
        L.polyline(sampleCableCoords, cableStyle).addTo(map4);

        // Add sample landing points
        const landingPoints = [
            [-26.2, 28.0, "Mtunzini, South Africa"],
            [-4.0, 39.7, "Mombasa, Kenya"],
            [11.6, 43.1, "Djibouti City"],
            [19.0, 72.8, "Mumbai, India"]
        ];

        const pointStyle = {
            color: '#ff0000',
            fillColor: '#ff0000',
            fillOpacity: 0.8,
            radius: 5
        };

        landingPoints.forEach(([lat, lng, name]) => {
            L.circleMarker([lat, lng], pointStyle).bindPopup(name).addTo(map1);
            L.circleMarker([lat, lng], pointStyle).bindPopup(name).addTo(map2);
            L.circleMarker([lat, lng], pointStyle).bindPopup(name).addTo(map3);
            L.circleMarker([lat, lng], pointStyle).bindPopup(name).addTo(map4);
        });

        // Synchronize map movements (optional - for better comparison)
        function syncMaps(sourceMap, targetMaps) {
            sourceMap.on('moveend', function() {
                const center = sourceMap.getCenter();
                const zoom = sourceMap.getZoom();
                targetMaps.forEach(map => {
                    map.setView(center, zoom);
                });
            });
        }

        // Sync all maps with map1 as the source
        syncMaps(map1, [map2, map3, map4]);
    </script>
</body>
</html>
