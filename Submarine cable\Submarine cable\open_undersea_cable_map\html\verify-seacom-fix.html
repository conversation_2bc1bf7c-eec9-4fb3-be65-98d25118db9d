<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEACOM Cable Fix Verification</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 SEACOM Cable Fix Verification</h1>
    <p>This page verifies that the SEACOM/Tata TGN-Eurasia cable is now properly included in the map display.</p>
    
    <div id="results"></div>

    <script>
        async function verifySeacomFix() {
            const resultsDiv = document.getElementById('results');
            
            try {
                // Test 1: Check if SEACOM cable exists in the data
                resultsDiv.innerHTML += '<h2>Test 1: Cable Data Existence</h2>';
                
                const response = await fetch('../cable/cable-geo.json');
                const data = await response.json();
                
                const seacomCable = data.features.find(feature => 
                    feature.properties.id === 'seacomtata-tgn-eurasia'
                );
                
                if (seacomCable) {
                    resultsDiv.innerHTML += '<div class="result success">✅ SEACOM/Tata TGN-Eurasia cable found in cable-geo.json</div>';
                    resultsDiv.innerHTML += `<div class="result info">Cable name: ${seacomCable.properties.name}</div>`;
                } else {
                    resultsDiv.innerHTML += '<div class="result error">❌ SEACOM/Tata TGN-Eurasia cable NOT found in cable-geo.json</div>';
                    return;
                }
                
                // Test 2: Check if cable is in critical African cables list
                resultsDiv.innerHTML += '<h2>Test 2: Critical African Cables List</h2>';
                
                const criticalAfricanCables = new Set([
                    '2africa',
                    'west-africa-cable-system-wacs',
                    'africa-coast-to-europe-ace',
                    'eastern-africa-submarine-system-eassy',
                    'asia-africa-europe-1-aae-1',
                    'safe',
                    'sat-3wasc',
                    'equiano',
                    'africa-1',
                    'seychelles-to-east-africa-system-seas',
                    'the-east-african-marine-system-teams',
                    'seacomtata-tgn-eurasia'
                ]);
                
                if (criticalAfricanCables.has('seacomtata-tgn-eurasia')) {
                    resultsDiv.innerHTML += '<div class="result success">✅ SEACOM cable is in critical African cables list</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="result error">❌ SEACOM cable is NOT in critical African cables list</div>';
                }
                
                // Test 3: Check Asia-Pacific filtering logic
                resultsDiv.innerHTML += '<h2>Test 3: Asia-Pacific Filtering Logic</h2>';
                
                function isInAsiaPacificRegion(coordinates) {
                    if (!coordinates || !Array.isArray(coordinates)) return false;

                    function checkCoordinates(coords) {
                        if (!Array.isArray(coords)) return false;

                        if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                            const lng = coords[0];
                            const lat = coords[1];

                            const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
                            const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

                            return inMainAsiaPacific || inPacificExtension;
                        }

                        for (let item of coords) {
                            if (checkCoordinates(item)) {
                                return true;
                            }
                        }

                        return false;
                    }

                    return checkCoordinates(coordinates);
                }
                
                const wouldBeFilteredByAsiaPacific = isInAsiaPacificRegion(seacomCable.geometry.coordinates);
                
                if (wouldBeFilteredByAsiaPacific) {
                    resultsDiv.innerHTML += '<div class="result warning">⚠️ SEACOM cable has coordinates in Asia-Pacific region (Mumbai, India)</div>';
                    resultsDiv.innerHTML += '<div class="result info">This is expected - the cable connects to Mumbai, India (longitude > 65°E)</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="result info">SEACOM cable does not have coordinates in Asia-Pacific region</div>';
                }
                
                // Test 4: Simulate the filtering logic
                resultsDiv.innerHTML += '<h2>Test 4: Filtering Logic Simulation</h2>';
                
                let wouldBePreserved = false;
                let preservationReason = '';
                
                // Check if it would be preserved by critical African cables exception
                if (criticalAfricanCables.has(seacomCable.properties.id)) {
                    wouldBePreserved = true;
                    preservationReason = 'Listed in critical African cables';
                } else if (wouldBeFilteredByAsiaPacific) {
                    wouldBePreserved = false;
                    preservationReason = 'Would be filtered due to Asia-Pacific coordinates';
                } else {
                    wouldBePreserved = true;
                    preservationReason = 'No filtering criteria met';
                }
                
                if (wouldBePreserved) {
                    resultsDiv.innerHTML += `<div class="result success">✅ SEACOM cable WOULD BE PRESERVED</div>`;
                    resultsDiv.innerHTML += `<div class="result info">Reason: ${preservationReason}</div>`;
                } else {
                    resultsDiv.innerHTML += `<div class="result error">❌ SEACOM cable WOULD BE FILTERED OUT</div>`;
                    resultsDiv.innerHTML += `<div class="result info">Reason: ${preservationReason}</div>`;
                }
                
                // Test 5: Check landing points
                resultsDiv.innerHTML += '<h2>Test 5: Landing Points Check</h2>';
                
                const landingPointResponse = await fetch('../landing-point/landing-point-geo.json');
                const landingPointData = await landingPointResponse.json();
                
                const seacomLandingPoints = [
                    'djibouti-city-djibouti',
                    'zafarana-egypt', 
                    'mumbai-india',
                    'mombasa-kenya',
                    'maputo-mozambique',
                    'jeddah-saudi-arabia',
                    'mtunzini-south-africa',
                    'dar-es-salaam-tanzania'
                ];
                
                const criticalAfricanLandingPoints = new Set([
                    'cape-town-south-africa',
                    'mtunzini-south-africa',
                    'port-elizabeth-south-africa',
                    'gqeberha-south-africa',
                    'mombasa-kenya',
                    'dar-es-salaam-tanzania',
                    'djibouti-city-djibouti',
                    'lagos-nigeria',
                    'accra-ghana',
                    'dakar-senegal',
                    'casablanca-morocco',
                    'alexandria-egypt',
                    'port-said-egypt',
                    'zafarana-egypt',
                    'mumbai-india',
                    'maputo-mozambique',
                    'jeddah-saudi-arabia'
                ]);
                
                let preservedLandingPoints = 0;
                let foundLandingPoints = 0;
                
                seacomLandingPoints.forEach(lpId => {
                    const landingPoint = landingPointData.features.find(f => f.properties.id === lpId);
                    if (landingPoint) {
                        foundLandingPoints++;
                        if (criticalAfricanLandingPoints.has(lpId)) {
                            preservedLandingPoints++;
                        }
                    }
                });
                
                resultsDiv.innerHTML += `<div class="result info">SEACOM landing points found: ${foundLandingPoints}/${seacomLandingPoints.length}</div>`;
                resultsDiv.innerHTML += `<div class="result info">SEACOM landing points preserved: ${preservedLandingPoints}/${seacomLandingPoints.length}</div>`;
                
                if (preservedLandingPoints === seacomLandingPoints.length) {
                    resultsDiv.innerHTML += '<div class="result success">✅ All SEACOM landing points are preserved</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="result warning">⚠️ Some SEACOM landing points may be filtered</div>';
                }
                
                // Final summary
                resultsDiv.innerHTML += '<h2>📋 Summary</h2>';
                
                if (wouldBePreserved && preservedLandingPoints === seacomLandingPoints.length) {
                    resultsDiv.innerHTML += '<div class="result success"><strong>🎉 SUCCESS: SEACOM/Tata TGN-Eurasia cable should now be visible on the map!</strong></div>';
                    resultsDiv.innerHTML += '<div class="result info">The cable and all its landing points are properly preserved by the filtering logic.</div>';
                } else {
                    resultsDiv.innerHTML += '<div class="result error"><strong>❌ ISSUE: SEACOM cable may still have visibility problems</strong></div>';
                    if (!wouldBePreserved) {
                        resultsDiv.innerHTML += '<div class="result error">- Cable is not being preserved by filtering logic</div>';
                    }
                    if (preservedLandingPoints < seacomLandingPoints.length) {
                        resultsDiv.innerHTML += '<div class="result error">- Some landing points are not being preserved</div>';
                    }
                }
                
            } catch (error) {
                resultsDiv.innerHTML += `<div class="result error">❌ Error during verification: ${error.message}</div>`;
                console.error('Verification error:', error);
            }
        }
        
        // Run verification when page loads
        verifySeacomFix();
    </script>
</body>
</html>
