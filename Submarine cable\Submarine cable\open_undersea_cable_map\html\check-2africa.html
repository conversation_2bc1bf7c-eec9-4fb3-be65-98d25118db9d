<!DOCTYPE html>
<html>
<head>
    <title>2Africa Cable Specific Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .result { margin: 15px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .code { font-family: monospace; background-color: #f8f9fa; padding: 10px; border-radius: 3px; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 2Africa Cable Specific Investigation</h1>
    
    <div class="result info">
        <h3>What we're checking:</h3>
        <ul>
            <li>Is the 2Africa cable present in cable-geo.json?</li>
            <li>Does it have coordinate data?</li>
            <li>Is it being filtered by current logic?</li>
            <li>What are its exact coordinates?</li>
        </ul>
    </div>

    <div id="results"></div>

    <script>
        const resultsDiv = document.getElementById('results');

        function addResult(title, content, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<h3>${title}</h3>${content}`;
            resultsDiv.appendChild(div);
        }

        // Copy filtering functions
        function isInAmericasRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];
                    return (lng >= -180 && lng <= -30) && (lat >= -60 && lat <= 85);
                }

                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }

            return checkCoordinates(coordinates);
        }

        function isInAsiaPacificRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];
                    const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
                    const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);
                    return inMainAsiaPacific || inPacificExtension;
                }

                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }

            return checkCoordinates(coordinates);
        }

        // Load and check 2Africa specifically
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                addResult('✅ Data Loading', `Successfully loaded ${data.features.length} cables from cable-geo.json`, 'success');

                // Find 2Africa cable
                const twoAfricaCable = data.features.find(feature => 
                    feature.properties.id === '2africa' || 
                    feature.properties.name.toLowerCase().includes('2africa')
                );

                if (!twoAfricaCable) {
                    addResult('❌ 2Africa Cable Not Found', 
                        `The 2Africa cable is not present in cable-geo.json. This means it has no coordinate data and cannot be displayed on the map.
                        <br><br><strong>Possible causes:</strong>
                        <ul>
                            <li>Cable data is incomplete</li>
                            <li>2Africa cable was not processed into the GeoJSON file</li>
                            <li>Cable ID mismatch</li>
                        </ul>`, 'error');
                    return;
                }

                addResult('✅ 2Africa Cable Found', 
                    `Found 2Africa cable in dataset:
                    <div class="code">
                        <strong>Name:</strong> ${twoAfricaCable.properties.name}<br>
                        <strong>ID:</strong> ${twoAfricaCable.properties.id}<br>
                        <strong>Length:</strong> ${twoAfricaCable.properties.length || 'N/A'}<br>
                        <strong>RFS:</strong> ${twoAfricaCable.properties.rfs || 'N/A'}<br>
                        <strong>Owners:</strong> ${twoAfricaCable.properties.owners || 'N/A'}
                    </div>`, 'success');

                // Check coordinates
                const coords = twoAfricaCable.geometry ? twoAfricaCable.geometry.coordinates : null;
                
                if (!coords) {
                    addResult('❌ No Coordinates', 
                        'The 2Africa cable has no coordinate data in its geometry. This is why it cannot be displayed on the map.', 'error');
                    return;
                }

                addResult('✅ Coordinates Found', 
                    `2Africa cable has coordinate data:
                    <div class="code">
                        <strong>Geometry Type:</strong> ${twoAfricaCable.geometry.type}<br>
                        <strong>Coordinate Structure:</strong> ${Array.isArray(coords) ? `Array with ${coords.length} elements` : 'Not an array'}<br>
                        <strong>First few coordinates:</strong><br>
                        <pre>${JSON.stringify(coords.slice(0, 3), null, 2)}</pre>
                    </div>`, 'success');

                // Test filtering
                const isAmericas = isInAmericasRegion(coords);
                const isAsiaPacific = isInAsiaPacificRegion(coords);
                const wouldBeFiltered = isAmericas || isAsiaPacific;

                if (wouldBeFiltered) {
                    addResult('❌ Cable Being Filtered', 
                        `The 2Africa cable is being filtered out:
                        <ul>
                            <li><strong>Americas region:</strong> ${isAmericas ? '❌ YES (being filtered)' : '✅ No'}</li>
                            <li><strong>Asia-Pacific region:</strong> ${isAsiaPacific ? '❌ YES (being filtered)' : '✅ No'}</li>
                        </ul>
                        <p>This explains why the cable is not visible on the map.</p>`, 'error');
                } else {
                    addResult('✅ Cable Should Be Visible', 
                        `The 2Africa cable is NOT being filtered:
                        <ul>
                            <li><strong>Americas region:</strong> ${isAmericas ? '❌ YES' : '✅ No'}</li>
                            <li><strong>Asia-Pacific region:</strong> ${isAsiaPacific ? '❌ YES' : '✅ No'}</li>
                        </ul>
                        <p>The cable should be visible on the map. If it's not, there may be a rendering or map view issue.</p>`, 'success');
                }

                // Analyze coordinate ranges
                let minLng = Infinity, maxLng = -Infinity;
                let minLat = Infinity, maxLat = -Infinity;
                let coordCount = 0;

                function analyzeCoords(c) {
                    if (Array.isArray(c) && c.length === 2 && typeof c[0] === 'number' && typeof c[1] === 'number') {
                        const [lng, lat] = c;
                        minLng = Math.min(minLng, lng);
                        maxLng = Math.max(maxLng, lng);
                        minLat = Math.min(minLat, lat);
                        maxLat = Math.max(maxLat, lat);
                        coordCount++;
                    } else if (Array.isArray(c)) {
                        c.forEach(analyzeCoords);
                    }
                }

                analyzeCoords(coords);

                addResult('📊 Coordinate Analysis', 
                    `2Africa cable coordinate ranges:
                    <div class="code">
                        <strong>Longitude:</strong> ${minLng.toFixed(2)}° to ${maxLng.toFixed(2)}°<br>
                        <strong>Latitude:</strong> ${minLat.toFixed(2)}° to ${maxLat.toFixed(2)}°<br>
                        <strong>Total coordinate points:</strong> ${coordCount}<br>
                        <strong>Geographic span:</strong> ${(maxLng - minLng).toFixed(2)}° longitude, ${(maxLat - minLat).toFixed(2)}° latitude
                    </div>
                    
                    <h4>Regional Coverage:</h4>
                    <ul>
                        <li><strong>Africa:</strong> ${(minLng >= -20 && maxLng <= 55) ? '✅ Fully within Africa' : '⚠️ Extends beyond Africa'}</li>
                        <li><strong>Europe:</strong> ${(maxLat > 35) ? '✅ Reaches Europe' : '❌ Does not reach Europe'}</li>
                        <li><strong>Asia:</strong> ${(maxLng > 55) ? '✅ Reaches Asia' : '❌ Does not reach Asia'}</li>
                    </ul>`, 'info');

                // Final diagnosis
                let diagnosis = '<h4>🔧 Diagnosis:</h4>';
                
                if (wouldBeFiltered) {
                    diagnosis += `<p><strong>ISSUE FOUND:</strong> The 2Africa cable is being filtered out by the current filtering logic.</p>
                    <p><strong>SOLUTION:</strong> Add 2Africa to the exception list or adjust filtering boundaries.</p>`;
                } else {
                    diagnosis += `<p><strong>FILTERING OK:</strong> The 2Africa cable should be visible based on filtering logic.</p>
                    <p><strong>POSSIBLE ISSUES:</strong></p>
                    <ul>
                        <li>Map is not zoomed to the right area (try zooming out to see Africa)</li>
                        <li>Cable styling makes it hard to see (try different colors)</li>
                        <li>JavaScript errors preventing rendering</li>
                        <li>Map initialization issues</li>
                    </ul>`;
                }

                addResult('🎯 Final Diagnosis', diagnosis, wouldBeFiltered ? 'error' : 'warning');

                // Console output
                console.log('=== 2AFRICA CABLE ANALYSIS ===');
                console.log('Cable found:', !!twoAfricaCable);
                console.log('Has coordinates:', !!coords);
                console.log('Is Americas:', isAmericas);
                console.log('Is Asia-Pacific:', isAsiaPacific);
                console.log('Would be filtered:', wouldBeFiltered);
                console.log('Coordinate ranges:', { minLng, maxLng, minLat, maxLat });
                console.log('Full cable data:', twoAfricaCable);
            })
            .catch(error => {
                addResult('❌ Error Loading Data', 
                    `Failed to load cable data: ${error.message}
                    <br><br>This indicates a fundamental problem with data loading.`, 'error');
                console.error('Error:', error);
            });
    </script>
</body>
</html>
