<!DOCTYPE html>
<html>
<head>
    <title>African Cables Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .cable-item { margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 3px; }
        .coordinates { font-family: monospace; font-size: 11px; color: #666; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .step { margin: 15px 0; padding: 10px; background-color: #e9ecef; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <h1>🔍 African Submarine Cables Debug Tool</h1>
    
    <div class="debug-section info">
        <h3>Debug Process</h3>
        <p>This tool will systematically check why African cables are not visible:</p>
        <ol>
            <li>Load cable data and verify African cables exist</li>
            <li>Test current filtering logic against African cables</li>
            <li>Check coordinate ranges and geographic boundaries</li>
            <li>Analyze specific cables (2Africa, WACS, ACE, etc.)</li>
            <li>Provide specific recommendations</li>
        </ol>
    </div>

    <div id="debug-results"></div>

    <script>
        // Copy exact filtering functions from main implementation
        function isInAmericasRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];
                    
                    // Americas region boundaries:
                    // Longitude: -180°W to -30°W, Latitude: -60°S to 85°N
                    return (lng >= -180 && lng <= -30) && (lat >= -60 && lat <= 85);
                }

                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }

            return checkCoordinates(coordinates);
        }

        function isInAsiaPacificRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];
                    
                    // Asia-Pacific region boundaries (UPDATED):
                    // Main Asia-Pacific: Longitude 65°E to 180°E, Latitude -50°S to 80°N
                    // Pacific extension: Longitude -180°E to -120°W (for Pacific islands), Latitude -50°S to 80°N
                    const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
                    const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);
                    
                    return inMainAsiaPacific || inPacificExtension;
                }

                for (let item of coords) {
                    if (checkCoordinates(item)) return true;
                }
                return false;
            }

            return checkCoordinates(coordinates);
        }

        // African cables to specifically check
        const africanCableIds = [
            '2africa',
            'west-africa-cable-system-wacs',
            'africa-coast-to-europe-ace',
            'eastern-africa-submarine-system-eassy',
            'asia-africa-europe-1-aae-1',
            'safe',
            'sat-3wasc',
            'equiano'
        ];

        function debugStep(title, content, type = 'info') {
            const resultsDiv = document.getElementById('debug-results');
            const stepDiv = document.createElement('div');
            stepDiv.className = `debug-section ${type}`;
            stepDiv.innerHTML = `<h3>${title}</h3>${content}`;
            resultsDiv.appendChild(stepDiv);
        }

        function analyzeCoordinates(coords, cableName) {
            if (!coords || !Array.isArray(coords)) {
                return { hasCoords: false, sample: null, ranges: null };
            }

            let minLng = Infinity, maxLng = -Infinity;
            let minLat = Infinity, maxLat = -Infinity;
            let sampleCoord = null;

            function extractCoords(c) {
                if (Array.isArray(c) && c.length === 2 && typeof c[0] === 'number' && typeof c[1] === 'number') {
                    const [lng, lat] = c;
                    minLng = Math.min(minLng, lng);
                    maxLng = Math.max(maxLng, lng);
                    minLat = Math.min(minLat, lat);
                    maxLat = Math.max(maxLat, lat);
                    if (!sampleCoord) sampleCoord = [lng, lat];
                } else if (Array.isArray(c)) {
                    c.forEach(extractCoords);
                }
            }

            extractCoords(coords);

            return {
                hasCoords: sampleCoord !== null,
                sample: sampleCoord,
                ranges: sampleCoord ? {
                    lng: { min: minLng, max: maxLng },
                    lat: { min: minLat, max: maxLat }
                } : null
            };
        }

        // Start debugging process
        debugStep('🔄 Step 1: Loading Cable Data', '<p>Fetching cable-geo.json...</p>');

        fetch('../cable/cable-geo.json')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                debugStep('✅ Step 1 Complete: Data Loaded Successfully', 
                    `<p>Total cables in dataset: <strong>${data.features.length}</strong></p>`);

                // Step 2: Find African cables
                debugStep('🔄 Step 2: Searching for African Cables', '<p>Looking for African cable systems...</p>');

                const foundAfricanCables = [];
                const allAfricanRelated = [];

                data.features.forEach(feature => {
                    const id = feature.properties.id;
                    const name = feature.properties.name;
                    
                    // Check if it's a known African cable
                    if (africanCableIds.includes(id)) {
                        foundAfricanCables.push(feature);
                    }
                    
                    // Check if it contains African keywords
                    if (name && (
                        name.toLowerCase().includes('africa') ||
                        name.toLowerCase().includes('wacs') ||
                        name.toLowerCase().includes('ace') ||
                        name.toLowerCase().includes('safe') ||
                        name.toLowerCase().includes('eassy')
                    )) {
                        allAfricanRelated.push(feature);
                    }
                });

                let step2Content = `
                    <p><strong>Known African cables found:</strong> ${foundAfricanCables.length}</p>
                    <p><strong>All African-related cables found:</strong> ${allAfricanRelated.length}</p>
                    <ul>
                `;

                foundAfricanCables.forEach(cable => {
                    step2Content += `<li><strong>${cable.properties.name}</strong> (${cable.properties.id})</li>`;
                });

                step2Content += '</ul>';

                debugStep('✅ Step 2 Complete: African Cables Found', step2Content, 
                    foundAfricanCables.length > 0 ? 'success' : 'error');

                // Step 3: Test filtering logic
                debugStep('🔄 Step 3: Testing Filtering Logic', '<p>Applying current filtering rules...</p>');

                const filterResults = [];
                foundAfricanCables.forEach(cable => {
                    const coords = cable.geometry ? cable.geometry.coordinates : null;
                    const coordAnalysis = analyzeCoordinates(coords, cable.properties.name);
                    
                    const isAmericas = coords ? isInAmericasRegion(coords) : false;
                    const isAsiaPacific = coords ? isInAsiaPacificRegion(coords) : false;
                    const wouldBeFiltered = isAmericas || isAsiaPacific;

                    filterResults.push({
                        cable,
                        coordAnalysis,
                        isAmericas,
                        isAsiaPacific,
                        wouldBeFiltered
                    });
                });

                let step3Content = '<table><tr><th>Cable</th><th>Has Coords</th><th>Sample Coord</th><th>Americas</th><th>Asia-Pacific</th><th>Filtered</th></tr>';
                
                filterResults.forEach(result => {
                    const { cable, coordAnalysis, isAmericas, isAsiaPacific, wouldBeFiltered } = result;
                    step3Content += `
                        <tr>
                            <td>${cable.properties.name}</td>
                            <td>${coordAnalysis.hasCoords ? '✅' : '❌'}</td>
                            <td class="coordinates">${coordAnalysis.sample ? JSON.stringify(coordAnalysis.sample) : 'None'}</td>
                            <td>${isAmericas ? '❌ Yes' : '✅ No'}</td>
                            <td>${isAsiaPacific ? '❌ Yes' : '✅ No'}</td>
                            <td>${wouldBeFiltered ? '❌ FILTERED' : '✅ VISIBLE'}</td>
                        </tr>
                    `;
                });
                
                step3Content += '</table>';

                const filteredCount = filterResults.filter(r => r.wouldBeFiltered).length;
                const visibleCount = filterResults.filter(r => !r.wouldBeFiltered).length;

                step3Content += `
                    <p><strong>Summary:</strong></p>
                    <ul>
                        <li>Cables that should be visible: <strong>${visibleCount}</strong></li>
                        <li>Cables being filtered: <strong>${filteredCount}</strong></li>
                    </ul>
                `;

                debugStep('✅ Step 3 Complete: Filtering Analysis', step3Content, 
                    filteredCount === 0 ? 'success' : 'error');

                // Step 4: Detailed coordinate analysis
                debugStep('🔄 Step 4: Coordinate Range Analysis', '<p>Analyzing coordinate ranges...</p>');

                let step4Content = '<h4>Detailed Coordinate Analysis:</h4>';
                
                filterResults.forEach(result => {
                    const { cable, coordAnalysis } = result;
                    if (coordAnalysis.hasCoords && coordAnalysis.ranges) {
                        const { ranges } = coordAnalysis;
                        step4Content += `
                            <div class="cable-item">
                                <strong>${cable.properties.name}</strong><br>
                                Longitude: ${ranges.lng.min.toFixed(2)}° to ${ranges.lng.max.toFixed(2)}°<br>
                                Latitude: ${ranges.lat.min.toFixed(2)}° to ${ranges.lat.max.toFixed(2)}°<br>
                                <span class="coordinates">Sample: ${JSON.stringify(coordAnalysis.sample)}</span>
                            </div>
                        `;
                    }
                });

                step4Content += `
                    <h4>Current Filter Boundaries:</h4>
                    <ul>
                        <li><strong>Americas:</strong> -180° to -30° longitude</li>
                        <li><strong>Asia-Pacific:</strong> 65° to 180° longitude (updated from 60°)</li>
                        <li><strong>African Range (typical):</strong> -20° to 55° longitude</li>
                    </ul>
                `;

                debugStep('✅ Step 4 Complete: Coordinate Analysis', step4Content);

                // Step 5: Recommendations
                let recommendations = '<h4>🔧 Troubleshooting Recommendations:</h4>';
                
                if (foundAfricanCables.length === 0) {
                    recommendations += `
                        <div class="error">
                            <strong>❌ CRITICAL ISSUE: No African cables found in dataset</strong>
                            <ul>
                                <li>Check if cable-geo.json contains African cable data</li>
                                <li>Verify data loading process</li>
                                <li>Check for data corruption or missing files</li>
                            </ul>
                        </div>
                    `;
                } else if (filteredCount > 0) {
                    recommendations += `
                        <div class="error">
                            <strong>❌ FILTERING ISSUE: ${filteredCount} African cables being filtered</strong>
                            <ul>
                                <li>Some African cables have coordinates in filtered regions</li>
                                <li>Consider adding African cable exceptions</li>
                                <li>Review boundary definitions</li>
                            </ul>
                        </div>
                    `;
                } else if (visibleCount > 0) {
                    recommendations += `
                        <div class="warning">
                            <strong>⚠️ RENDERING ISSUE: Cables should be visible but aren't showing</strong>
                            <ul>
                                <li>Check map zoom level and geographic view</li>
                                <li>Verify CSS styling for cable rendering</li>
                                <li>Check for JavaScript errors in browser console</li>
                                <li>Ensure map is centered on African region</li>
                            </ul>
                        </div>
                    `;
                }

                recommendations += `
                    <h4>🛠️ Immediate Actions:</h4>
                    <ol>
                        <li><strong>Check Browser Console:</strong> Open developer tools and look for errors</li>
                        <li><strong>Verify Map View:</strong> Zoom out and pan to Africa (coordinates: 0°, 0°)</li>
                        <li><strong>Check Network Tab:</strong> Ensure cable-geo.json loads successfully</li>
                        <li><strong>Test Specific Cable:</strong> Search for "2africa" in browser console</li>
                    </ol>
                `;

                debugStep('🎯 Step 5: Recommendations', recommendations, 'warning');

                // Console logging for debugging
                console.log('=== AFRICAN CABLES DEBUG RESULTS ===');
                console.log(`Total cables: ${data.features.length}`);
                console.log(`African cables found: ${foundAfricanCables.length}`);
                console.log(`Cables being filtered: ${filteredCount}`);
                console.log(`Cables should be visible: ${visibleCount}`);
                console.log('Filter results:', filterResults);

            })
            .catch(error => {
                debugStep('❌ ERROR: Failed to Load Data', 
                    `<p><strong>Error:</strong> ${error.message}</p>
                     <p>This indicates a fundamental data loading issue.</p>
                     <ul>
                        <li>Check if cable-geo.json file exists</li>
                        <li>Verify file permissions</li>
                        <li>Check network connectivity</li>
                        <li>Look for CORS issues in browser console</li>
                     </ul>`, 'error');
                
                console.error('Failed to load cable data:', error);
            });
    </script>
</body>
</html>
