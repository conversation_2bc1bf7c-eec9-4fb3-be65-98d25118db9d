{"id": "mednautilus-submarine-system", "name": "MedNautilus Submarine System", "length": "7,000 km", "landing_points": [{"id": "pentaskhinos-cyprus", "name": "Pentaskhinos, Cyprus", "country": "Cyprus", "is_tbd": null}, {"id": "athens-greece", "name": "Athens, Greece", "country": "Greece", "is_tbd": null}, {"id": "chania-greece", "name": "Chania, Greece", "country": "Greece", "is_tbd": null}, {"id": "haifa-israel", "name": "Haifa, Israel", "country": "Israel", "is_tbd": null}, {"id": "tel-aviv-israel", "name": "Tel Aviv, Israel", "country": "Israel", "is_tbd": null}, {"id": "catania-italy", "name": "Catania, Italy", "country": "Italy", "is_tbd": null}, {"id": "istanbul-turkey", "name": "Istanbul, Turkey", "country": "Turkey", "is_tbd": null}], "owners": "Telecom Italia Sparkle", "suppliers": "ASN", "rfs": "2001 November", "rfs_year": 2001, "is_planned": false, "url": "https://www.globalbackbone.tisparkle.com/", "notes": null}